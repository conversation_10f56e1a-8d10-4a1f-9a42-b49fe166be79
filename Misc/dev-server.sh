#!/bin/bash

# When Did I Take - 開發伺服器管理腳本
# 版本: 1.0.0
# 作者: <PERSON> Assistant

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置變數
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
LOGS_DIR="${SCRIPT_DIR}/Logs"
ENV_FILE="${SCRIPT_DIR}/.env.dev"

# 建立日誌目錄
mkdir -p "$LOGS_DIR"
BACKEND_PORTS=(8080 8081 8088)
FRONTEND_PORTS=(58080 58081 58088)

# 環境變數載入函數
load_env() {
    if [[ -f "$ENV_FILE" ]]; then
        source "$ENV_FILE"
    fi
}

# 環境變數儲存函數
save_env() {
    local key="$1"
    local value="$2"

    if [[ -f "$ENV_FILE" ]]; then
        # 更新現有的環境變數
        if grep -q "^${key}=" "$ENV_FILE"; then
            sed -i '' "s/^${key}=.*/${key}=${value}/" "$ENV_FILE"
        else
            echo "${key}=${value}" >> "$ENV_FILE"
        fi
    else
        echo "${key}=${value}" > "$ENV_FILE"
    fi
}

# 詢問使用者是否繼續
ask_yes_no() {
    local message="$1"
    local default="${2:-n}"

    while true; do
        if [[ "$default" == "y" ]]; then
            printf "${YELLOW}${message} [Y/n]: ${NC}"
        else
            printf "${YELLOW}${message} [y/N]: ${NC}"
        fi

        read -r response
        response=${response:-$default}

        case "$response" in
            [Yy]|[Yy][Ee][Ss])
                return 0
                ;;
            [Nn]|[Nn][Oo])
                return 1
                ;;
            *)
                echo -e "${RED}請回答 yes 或 no${NC}"
                ;;
        esac
    done
}

# 檢查port是否被佔用
check_port_usage() {
    local port="$1"
    lsof -ti:$port 2>/dev/null || true
}

# 檢查是否為本專案的伺服器進程
is_project_server() {
    local pid="$1"
    local server_type="$2"  # "backend" 或 "frontend"

    if [[ -z "$pid" ]]; then
        return 1
    fi

    local cmd_line
    cmd_line=$(ps -p "$pid" -o command= 2>/dev/null || echo "")

    case "$server_type" in
        "backend")
            if [[ "$cmd_line" == *"uvicorn"* && "$cmd_line" == *"when_did_i_take.api.main:app"* ]]; then
                return 0
            fi
            ;;
        "frontend")
            if [[ "$cmd_line" == *"vite"* && "$cmd_line" == *"dev"* ]]; then
                return 0
            fi
            ;;
    esac

    return 1
}

# 根據進程命令檢查伺服器狀態（取得所有進程）
get_server_pids() {
    local server_type="$1"

    case "$server_type" in
        "backend")
            # 檢查所有 uvicorn 後端伺服器，排除其他專案的 uvicorn
            pgrep -f "uvicorn.*when_did_i_take\.api\.main:app" 2>/dev/null || true
            ;;
        "frontend")
            # 檢查所有 Vite 前端伺服器（包括 node 進程）
            { pgrep -f "vite.*dev" 2>/dev/null || true; 
              pgrep -f "npm.*run.*dev" 2>/dev/null || true; 
              pgrep -f "pnpm.*dev" 2>/dev/null || true; 
              pgrep -f "node.*vite.*dev" 2>/dev/null || true; } | sort -u | grep -v '^$' || true
            ;;
    esac
}

# 為相容性保留舊函數（只返回第一個進程）
get_server_pid() {
    local server_type="$1"
    get_server_pids "$server_type" | head -1
}

# 根據PID獲取佔用的port
get_port_by_pid() {
    local pid="$1"
    if [[ -n "$pid" ]]; then
        # 先檢查該PID是否直接監聽port
        local port=$(lsof -Pan -p "$pid" -i 2>/dev/null | grep LISTEN | grep -o ':[0-9]*' | cut -d: -f2 | head -1)

        # 如果沒有找到，從命令行參數中提取port
        if [[ -z "$port" ]]; then
            local cmd_line=$(ps -p "$pid" -o command= 2>/dev/null || echo "")
            if [[ "$cmd_line" == *"uvicorn"* && "$cmd_line" == *"--port"* ]]; then
                # 從uvicorn命令行參數中提取port
                port=$(echo "$cmd_line" | grep -o -- '--port [0-9]*' | awk '{print $2}')
            elif [[ "$cmd_line" == *"vite"* && "$cmd_line" == *"--port"* ]]; then
                # 從vite命令行參數中提取port
                port=$(echo "$cmd_line" | grep -o -- '--port [0-9]*' | awk '{print $2}')
            fi
        fi

        echo "$port"
    fi
}

# 功能1: 檢查前後端伺服器狀態
check_servers() {
    echo -e "${BLUE}=== 檢查伺服器狀態 ===${NC}"

    load_env

    local backend_pids
    local frontend_pids
    local backend_port
    local frontend_port

    # 檢查後端
    backend_pids=$(get_server_pids "backend")
    if [[ -n "$backend_pids" ]]; then
        echo -e "${GREEN}✓ 找到後端伺服器進程${NC}"
        while IFS= read -r pid; do
            if [[ -n "$pid" ]]; then
                backend_port=$(get_port_by_pid "$pid")
                echo -e "  PID: ${CYAN}$pid${NC}, Port: ${CYAN}${backend_port:-未知}${NC}"
            fi
        done <<< "$backend_pids"
        echo -e "  環境變數Port: ${CYAN}${BACKEND_PORT:-未設定}${NC}"
    else
        echo -e "${RED}✗ 後端伺服器未運行${NC}"
    fi

    # 檢查前端
    frontend_pids=$(get_server_pids "frontend")
    if [[ -n "$frontend_pids" ]]; then
        echo -e "${GREEN}✓ 找到前端伺服器進程${NC}"
        while IFS= read -r pid; do
            if [[ -n "$pid" ]]; then
                frontend_port=$(get_port_by_pid "$pid")
                echo -e "  PID: ${CYAN}$pid${NC}, Port: ${CYAN}${frontend_port:-未知}${NC}"
            fi
        done <<< "$frontend_pids"
        echo -e "  環境變數Port: ${CYAN}${FRONTEND_PORT:-未設定}${NC}"
    else
        echo -e "${RED}✗ 前端伺服器未運行${NC}"
    fi

    # 為相容性保留舊格式（只返回第一個進程）
    BACKEND_PID=$(echo "$backend_pids" | head -1)
    FRONTEND_PID=$(echo "$frontend_pids" | head -1)

    echo ""
    return 0
}

# 決定要使用的port
determine_port() {
    local server_type="$1"
    local param_port="$2"
    local env_var_name
    local default_ports
    local current_env_port

    if [[ "$server_type" == "backend" ]]; then
        env_var_name="BACKEND_PORT"
        default_ports=("${BACKEND_PORTS[@]}")
    else
        env_var_name="FRONTEND_PORT"
        default_ports=("${FRONTEND_PORTS[@]}")
    fi

    # 讀取當前環境變數
    current_env_port=$(eval echo \$${env_var_name})

    local selected_port=""

    # 優先順序: 參數 > 環境變數 > 預設
    if [[ -n "$param_port" ]]; then
        # 有參數指定port
        if [[ -n "$current_env_port" && "$current_env_port" != "$param_port" ]]; then
            exec 3>&1
            if ask_yes_no "環境變數中${server_type}port為 $current_env_port，要更新為 $param_port 嗎？" >&3; then
                save_env "$env_var_name" "$param_port"
                echo "已更新環境變數 ${env_var_name}=${param_port}" >&3
            fi
            exec 3>&-
        else
            save_env "$env_var_name" "$param_port"
        fi
        selected_port="$param_port"
    elif [[ -n "$current_env_port" ]]; then
        # 使用環境變數
        selected_port="$current_env_port"
    else
        # 使用預設port（第一個可用的）
        for port in "${default_ports[@]}"; do
            local port_pid
            port_pid=$(check_port_usage "$port")
            if [[ -z "$port_pid" ]]; then
                selected_port="$port"
                save_env "$env_var_name" "$selected_port"
                break
            fi
        done

        if [[ -z "$selected_port" ]]; then
            return 1
        fi
    fi

    echo "$selected_port"
}

# 處理port衝突
handle_port_conflict() {
    local server_type="$1"
    local port="$2"
    local default_ports

    if [[ "$server_type" == "backend" ]]; then
        default_ports=("${BACKEND_PORTS[@]}")
    else
        default_ports=("${FRONTEND_PORTS[@]}")
    fi

    local port_pid
    port_pid=$(check_port_usage "$port")

    if [[ -n "$port_pid" ]]; then
        echo -e "${YELLOW}Port $port 已被佔用 (PID: $port_pid)${NC}"

        if is_project_server "$port_pid" "$server_type"; then
            echo -e "${CYAN}該port被本專案的${server_type}伺服器佔用${NC}"
            if ask_yes_no "是否要重啟該伺服器？"; then
                echo -e "${YELLOW}正在停止現有伺服器...${NC}"
                kill "$port_pid" 2>/dev/null || true
                sleep 2
                return 0
            else
                echo -e "${RED}取消啟動${NC}"
                return 1
            fi
        else
            echo -e "${RED}Port $port 被其他進程佔用${NC}"

            # 尋找下一個可用port
            local next_port=""
            for next in "${default_ports[@]}"; do
                if [[ "$next" != "$port" ]]; then
                    local next_pid
                    next_pid=$(check_port_usage "$next")
                    if [[ -z "$next_pid" ]]; then
                        next_port="$next"
                        break
                    fi
                fi
            done

            if [[ -n "$next_port" ]]; then
                if ask_yes_no "是否使用下一個可用port $next_port？"; then
                    # 更新環境變數
                    local env_var_name
                    if [[ "$server_type" == "backend" ]]; then
                        env_var_name="BACKEND_PORT"
                    else
                        env_var_name="FRONTEND_PORT"
                    fi
                    save_env "$env_var_name" "$next_port"
                    echo "$next_port"
                    return 0
                fi
            fi

            echo -e "${RED}無法解決port衝突${NC}"
            return 1
        fi
    fi

    echo "$port"
    return 0
}

# 啟動後端伺服器
start_backend() {
    local port="$1"

    echo -e "${BLUE}正在啟動後端伺服器 (Port: $port)...${NC}"

    cd "$PROJECT_ROOT"

    # 檢查是否有 uv 環境
    if [[ -f "$PROJECT_ROOT/backend/uv.lock" ]]; then
        echo -e "${CYAN}使用 uv + uvicorn 啟動後端伺服器${NC}"
        # 切換到後端目錄
        cd "$PROJECT_ROOT/backend"
        # 在背景啟動後端
        nohup uv run uvicorn when_did_i_take.api.main:app --host 0.0.0.0 --port "$port" --reload > "$LOGS_DIR/backend.log" 2>&1 &
        local backend_pid=$!
    else
        echo -e "${CYAN}使用 uvicorn 啟動後端伺服器${NC}"
        # 檢查虛擬環境
        if [[ -d "$PROJECT_ROOT/backend/.venv" ]]; then
            source "$PROJECT_ROOT/backend/.venv/bin/activate"
        fi
        # 切換到後端目錄
        cd "$PROJECT_ROOT/backend"
        nohup uvicorn when_did_i_take.api.main:app --host 0.0.0.0 --port "$port" --reload > "$LOGS_DIR/backend.log" 2>&1 &
        local backend_pid=$!
    fi

    sleep 3

    # 驗證啟動成功
    if ps -p "$backend_pid" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 後端伺服器啟動成功 (PID: $backend_pid, Port: $port)${NC}"
        echo -e "${CYAN}  後端API地址: http://localhost:$port${NC}"
        echo -e "${CYAN}  GraphQL Playground: http://localhost:$port/graphql${NC}"
        echo -e "${CYAN}  健康檢查: http://localhost:$port/health${NC}"
        echo -e "${CYAN}  日誌檔案: $LOGS_DIR/backend.log${NC}"
        return 0
    else
        echo -e "${RED}✗ 後端伺服器啟動失敗${NC}"
        echo -e "${YELLOW}檢查 $LOGS_DIR/backend.log 查看錯誤詳情${NC}"
        return 1
    fi
}

# 啟動前端伺服器
start_frontend() {
    local port="$1"

    echo -e "${BLUE}正在啟動前端伺服器 (Port: $port)...${NC}"

    cd "$PROJECT_ROOT/webapp"

    # 檢查 node_modules
    if [[ ! -d "node_modules" ]]; then
        echo -e "${YELLOW}正在安裝前端依賴...${NC}"
        if command -v pnpm &> /dev/null; then
            pnpm install
        else
            npm install
        fi
    fi

    # 在背景啟動前端
    if command -v pnpm &> /dev/null; then
        echo -e "${CYAN}使用 pnpm 啟動前端伺服器${NC}"
        nohup pnpm dev --port "$port" > "$LOGS_DIR/frontend.log" 2>&1 &
    else
        echo -e "${CYAN}使用 npm 啟動前端伺服器${NC}"
        nohup npm run dev -- --port "$port" > "$LOGS_DIR/frontend.log" 2>&1 &
    fi

    local frontend_pid=$!
    sleep 5

    # 驗證啟動成功
    if ps -p "$frontend_pid" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 前端伺服器啟動成功 (PID: $frontend_pid, Port: $port)${NC}"
        echo -e "${CYAN}  前端應用地址: http://localhost:$port${NC}"
        echo -e "${CYAN}  日誌檔案: $LOGS_DIR/frontend.log${NC}"
        cd "$SCRIPT_DIR"
        return 0
    else
        echo -e "${RED}✗ 前端伺服器啟動失敗${NC}"
        echo -e "${YELLOW}檢查 $LOGS_DIR/frontend.log 查看錯誤詳情${NC}"
        cd "$SCRIPT_DIR"
        return 1
    fi
}

# 功能2: 啟動開發伺服器
start_servers() {
    local backend_port_param="$1"
    local frontend_port_param="$2"

    echo -e "${BLUE}=== 啟動開發伺服器 ===${NC}"

    load_env

    # 簡化的port決定邏輯
    local backend_port="$backend_port_param"
    local frontend_port="$frontend_port_param"

    # 如果沒有指定port，使用環境變數或預設值
    if [[ -z "$backend_port" ]]; then
        if [[ -n "$BACKEND_PORT" ]]; then
            backend_port="$BACKEND_PORT"
            echo -e "${CYAN}使用環境變數中的後端port: $backend_port${NC}"
        else
            backend_port="${BACKEND_PORTS[0]}"
            save_env "BACKEND_PORT" "$backend_port"
            echo -e "${GREEN}使用預設後端port: $backend_port${NC}"
        fi
    else
        # 檢查是否需要更新環境變數
        if [[ -n "$BACKEND_PORT" && "$BACKEND_PORT" != "$backend_port" ]]; then
            if ask_yes_no "環境變數中後端port為 $BACKEND_PORT，要更新為 $backend_port 嗎？"; then
                save_env "BACKEND_PORT" "$backend_port"
                echo -e "${GREEN}已更新環境變數 BACKEND_PORT=${backend_port}${NC}"
            fi
        else
            save_env "BACKEND_PORT" "$backend_port"
        fi
    fi

    if [[ -z "$frontend_port" ]]; then
        if [[ -n "$FRONTEND_PORT" ]]; then
            frontend_port="$FRONTEND_PORT"
            echo -e "${CYAN}使用環境變數中的前端port: $frontend_port${NC}"
        else
            frontend_port="${FRONTEND_PORTS[0]}"
            save_env "FRONTEND_PORT" "$frontend_port"
            echo -e "${GREEN}使用預設前端port: $frontend_port${NC}"
        fi
    else
        # 檢查是否需要更新環境變數
        if [[ -n "$FRONTEND_PORT" && "$FRONTEND_PORT" != "$frontend_port" ]]; then
            if ask_yes_no "環境變數中前端port為 $FRONTEND_PORT，要更新為 $frontend_port 嗎？"; then
                save_env "FRONTEND_PORT" "$frontend_port"
                echo -e "${GREEN}已更新環境變數 FRONTEND_PORT=${frontend_port}${NC}"
            fi
        else
            save_env "FRONTEND_PORT" "$frontend_port"
        fi
    fi

    echo ""
    echo -e "${GREEN}準備啟動伺服器:${NC}"
    echo -e "  後端Port: ${CYAN}$backend_port${NC}"
    echo -e "  前端Port: ${CYAN}$frontend_port${NC}"
    echo ""

    # 檢查並更新前端配置
    check_and_update_frontend_config "$backend_port"
    echo ""

    # 啟動後端
    if ! start_backend "$backend_port"; then
        return 1
    fi

    echo ""

    # 啟動前端
    if ! start_frontend "$frontend_port"; then
        echo -e "${YELLOW}前端啟動失敗，是否要停止已啟動的後端？${NC}"
        if ask_yes_no "停止後端伺服器？"; then
            local current_backend_pids
            current_backend_pids=$(get_server_pids "backend")
            if [[ -n "$current_backend_pids" ]]; then
                while IFS= read -r current_backend_pid; do
                    if [[ -n "$current_backend_pid" ]]; then
                        kill "$current_backend_pid" 2>/dev/null || true
                    fi
                done <<< "$current_backend_pids"
                echo -e "${YELLOW}已停止所有後端伺服器${NC}"
            fi
        fi
        return 1
    fi

    echo ""
    echo -e "${GREEN}🎉 開發伺服器啟動完成！${NC}"
    echo -e "${CYAN}前端: http://localhost:$frontend_port${NC}"
    echo -e "${CYAN}後端: http://localhost:$backend_port${NC}"
    echo ""
    echo -e "${YELLOW}提示: 使用 '$0 stop' 停止伺服器${NC}"
    echo -e "${YELLOW}提示: 使用 '$0 check' 檢查伺服器狀態${NC}"
}

# 功能3: 停止伺服器
stop_servers() {
    echo -e "${BLUE}=== 停止伺服器 ===${NC}"

    load_env

    local backend_pids
    local frontend_pids
    local backend_port
    local frontend_port

    # 獲取所有伺服器PIDs
    backend_pids=$(get_server_pids "backend")
    frontend_pids=$(get_server_pids "frontend")

    if [[ -z "$backend_pids" && -z "$frontend_pids" ]]; then
        echo -e "${YELLOW}沒有找到運行中的伺服器${NC}"
        return 0
    fi

    # 處理後端
    if [[ -n "$backend_pids" ]]; then
        echo -e "${YELLOW}找到後端伺服器進程:${NC}"
        while IFS= read -r backend_pid; do
            if [[ -n "$backend_pid" ]]; then
                backend_port=$(get_port_by_pid "$backend_pid")
                echo -e "  PID: ${CYAN}$backend_pid${NC}, Port: ${CYAN}${backend_port:-未知}${NC}"
            fi
        done <<< "$backend_pids"

        local should_stop_backend=true
        if [[ -n "$BACKEND_PORT" ]]; then
            local has_mismatch=false
            while IFS= read -r backend_pid; do
                if [[ -n "$backend_pid" ]]; then
                    backend_port=$(get_port_by_pid "$backend_pid")
                    if [[ -n "$backend_port" && "$BACKEND_PORT" != "$backend_port" ]]; then
                        has_mismatch=true
                        break
                    fi
                fi
            done <<< "$backend_pids"
            
            if [[ "$has_mismatch" == true ]]; then
                echo -e "${YELLOW}警告: 有些運行中的後端port與環境變數不符 ($BACKEND_PORT)${NC}"
                if ! ask_yes_no "確定要停止所有後端伺服器嗎？"; then
                    should_stop_backend=false
                fi
            fi
        fi

        if [[ "$should_stop_backend" == true ]]; then
            echo -e "${YELLOW}正在停止後端伺服器...${NC}"
            while IFS= read -r backend_pid; do
                if [[ -n "$backend_pid" ]]; then
                    echo -e "  停止 PID: ${CYAN}$backend_pid${NC}"
                    kill "$backend_pid" 2>/dev/null || true
                fi
            done <<< "$backend_pids"
            sleep 2

            # 驗證是否停止成功
            local remaining_pids=$(get_server_pids "backend")
            if [[ -z "$remaining_pids" ]]; then
                echo -e "${GREEN}✓ 所有後端伺服器已停止${NC}"
            else
                echo -e "${YELLOW}強制停止剩餘的後端伺服器...${NC}"
                while IFS= read -r remaining_pid; do
                    if [[ -n "$remaining_pid" ]]; then
                        echo -e "  強制停止 PID: ${CYAN}$remaining_pid${NC}"
                        kill -9 "$remaining_pid" 2>/dev/null || true
                    fi
                done <<< "$remaining_pids"
                echo -e "${GREEN}✓ 所有後端伺服器已強制停止${NC}"
            fi
        fi
    fi

    # 處理前端
    if [[ -n "$frontend_pids" ]]; then
        echo -e "${YELLOW}找到前端伺服器進程:${NC}"
        while IFS= read -r frontend_pid; do
            if [[ -n "$frontend_pid" ]]; then
                frontend_port=$(get_port_by_pid "$frontend_pid")
                echo -e "  PID: ${CYAN}$frontend_pid${NC}, Port: ${CYAN}${frontend_port:-未知}${NC}"
            fi
        done <<< "$frontend_pids"

        local should_stop_frontend=true
        if [[ -n "$FRONTEND_PORT" ]]; then
            local has_mismatch=false
            while IFS= read -r frontend_pid; do
                if [[ -n "$frontend_pid" ]]; then
                    frontend_port=$(get_port_by_pid "$frontend_pid")
                    if [[ -n "$frontend_port" && "$FRONTEND_PORT" != "$frontend_port" ]]; then
                        has_mismatch=true
                        break
                    fi
                fi
            done <<< "$frontend_pids"
            
            if [[ "$has_mismatch" == true ]]; then
                echo -e "${YELLOW}警告: 有些運行中的前端port與環境變數不符 ($FRONTEND_PORT)${NC}"
                if ! ask_yes_no "確定要停止所有前端伺服器嗎？"; then
                    should_stop_frontend=false
                fi
            fi
        fi

        if [[ "$should_stop_frontend" == true ]]; then
            echo -e "${YELLOW}正在停止前端伺服器...${NC}"
            while IFS= read -r frontend_pid; do
                if [[ -n "$frontend_pid" ]]; then
                    echo -e "  停止 PID: ${CYAN}$frontend_pid${NC}"
                    kill "$frontend_pid" 2>/dev/null || true
                fi
            done <<< "$frontend_pids"
            sleep 2

            # 驗證是否停止成功
            local remaining_pids=$(get_server_pids "frontend")
            if [[ -z "$remaining_pids" ]]; then
                echo -e "${GREEN}✓ 所有前端伺服器已停止${NC}"
            else
                echo -e "${YELLOW}強制停止剩餘的前端伺服器...${NC}"
                while IFS= read -r remaining_pid; do
                    if [[ -n "$remaining_pid" ]]; then
                        echo -e "  強制停止 PID: ${CYAN}$remaining_pid${NC}"
                        kill -9 "$remaining_pid" 2>/dev/null || true
                    fi
                done <<< "$remaining_pids"
                echo -e "${GREEN}✓ 所有前端伺服器已強制停止${NC}"
            fi
        fi
    fi

    echo -e "${GREEN}伺服器停止完成${NC}"
}

# 檢查並更新前端配置
check_and_update_frontend_config() {
    local backend_port="$1"
    local config_file="${PROJECT_ROOT}/webapp/static/config.json"
    local api_url="http://localhost:${backend_port}"

    # 檢查配置檔案是否存在
    if [[ ! -f "$config_file" ]]; then
        echo -e "${YELLOW}前端配置檔案不存在，正在創建...${NC}" >&2
        cat > "$config_file" << EOF
{
  "API_BASE_URL": "$api_url",
  "API_TIMEOUT": 10000,
  "APP_NAME": "When Did I Take",
  "APP_VERSION": "1.0.0"
}
EOF
        echo -e "${GREEN}已創建前端配置檔案: $config_file${NC}" >&2
        return 0
    fi

    # 檢查配置中的API_BASE_URL是否正確
    local current_api_url
    current_api_url=$(grep -o '"API_BASE_URL":[^,}]*' "$config_file" | cut -d'"' -f4)

    if [[ "$current_api_url" != "$api_url" ]]; then
        echo -e "${YELLOW}前端配置中的API URL ($current_api_url) 與後端port ($backend_port) 不符${NC}" >&2

        if ask_yes_no "是否要更新前端配置檔案中的API URL？"; then
            # 使用sed更新API_BASE_URL
            if command -v jq &> /dev/null; then
                # 使用jq更新（更安全）
                local temp_file=$(mktemp)
                jq ".API_BASE_URL = \"$api_url\"" "$config_file" > "$temp_file" && mv "$temp_file" "$config_file"
                echo -e "${GREEN}已使用jq更新前端配置檔案${NC}" >&2
            else
                # 使用sed更新（備選方案）
                sed -i '' "s|\"API_BASE_URL\":\"[^\"]*\"|\"API_BASE_URL\":\"$api_url\"|" "$config_file"
                echo -e "${GREEN}已使用sed更新前端配置檔案${NC}" >&2
            fi
        else
            echo -e "${YELLOW}警告: 前端可能無法正確連接到後端伺服器${NC}" >&2
        fi
    else
        echo -e "${GREEN}前端配置檔案API URL正確${NC}" >&2
    fi
}

# 顯示使用說明
show_help() {
    echo -e "${BLUE}When Did I Take - 開發伺服器管理腳本${NC}"
    echo ""
    echo -e "${YELLOW}使用方法:${NC}"
    echo "  $0 check                          檢查伺服器狀態"
    echo "  $0 start [backend_port] [frontend_port]  啟動開發伺服器"
    echo "  $0 stop                           停止伺服器"
    echo "  $0 help                           顯示此說明"
    echo ""
    echo -e "${YELLOW}範例:${NC}"
    echo "  $0 check                          # 檢查伺服器狀態"
    echo "  $0 start                          # 使用預設port啟動"
    echo "  $0 start 8080                     # 指定後端port啟動"
    echo "  $0 start 8080 3000               # 指定前後端port啟動"
    echo "  $0 stop                           # 停止所有伺服器"
    echo ""
    echo -e "${YELLOW}預設Port:${NC}"
    echo "  後端: ${CYAN}8080, 8081, 8088${NC}"
    echo "  前端: ${CYAN}58080, 58081, 58088${NC}"
    echo ""
    echo -e "${YELLOW}環境變數檔案:${NC} ${CYAN}.env.dev${NC}"
}

# 主函數
main() {
    local command="$1"

    case "$command" in
        "check")
            check_servers
            ;;
        "start")
            start_servers "$2" "$3"
            ;;
        "stop")
            stop_servers
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        "")
            echo -e "${RED}請指定操作命令${NC}"
            echo ""
            show_help
            exit 1
            ;;
        *)
            echo -e "${RED}未知命令: $command${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 執行主函數
main "$@"