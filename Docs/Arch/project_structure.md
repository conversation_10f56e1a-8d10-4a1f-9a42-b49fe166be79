# When Did I Take - 專案架構文檔

v0.1.0, 最後更新：2025/7/16

---

## 專案概述

「When Did I Take」是一個用藥追蹤系統，採用前後端分離架構，旨在幫助使用者記錄和管理用藥時間與劑量。專案使用 Python FastAPI 作為後端，Svelte 作為前端框架，並透過 GraphQL 進行資料交換。

## 整體架構

```mermaid
graph TD
    A[前端 Web 應用] --> B[GraphQL API]
    B --> C[FastAPI 後端]
    C --> D[SQLite 資料庫]

    subgraph "前端層"
        A1[Svelte 應用]
        A2[TypeScript]
        A3[Vite 構建工具]
    **end**

    subgraph "API 層"
        B1[GraphQL Schema]
        B2[認證中介軟體]
        B3[資料解析器]
    end

    subgraph "應用層"
        C1[FastAPI 路由]
        C2[業務邏輯]
        C3[資料存取層]
    end

    subgraph "資料層"
        D1[SQLModel ORM]
        D2[SQLite 資料庫]
    end

    A1 --> B1
    B1 --> C1
    C1 --> D1
    D1 --> D2
```

## 目錄結構

```
when_did_I_take/
├── main.py                 # 應用程式入口點
├── pyproject.toml          # Python 專案設定
├── uv.lock                 # 依賴鎖定檔
├── config.yaml             # 系統設定檔
├── when_did_i_take.db      # SQLite 資料庫檔案
├── when_did_i_take/        # 主要 Python 套件
│   ├── models.py           # 資料模型定義
│   ├── db.py               # 資料庫連接與操作
│   ├── auth.py             # 認證相關功能
│   ├── config.py           # 設定檔處理
│   ├── graphql_schema.py   # GraphQL Schema 定義
│   ├── graphql_auth.py     # GraphQL 認證機制
│   └── api/                # API 相關模組
│       ├── main.py         # FastAPI 主應用程式
│       ├── server.py       # 伺服器啟動邏輯
│       ├── auth_routes.py  # 認證路由
│       └── graphql.py      # GraphQL 路由設定
├── webapp/                 # 前端 Web 應用
│   ├── src/
│   │   ├── routes/         # Svelte 路由
│   │   └── lib/            # 共用元件與工具
│   ├── static/             # 靜態資源
│   └── package.json        # 前端依賴設定
└── tests/                  # 測試程式
    ├── test_*.py           # 單元測試
    └── create_test_data.py # 測試資料建立
```

## 資料模型關係

```mermaid
erDiagram
    User {
        int id PK
        string name
        string email
        string password_hash
        datetime created_at
    }

    Pharmaceutical {
        int id PK
        string name UK
        enum formulation_type
        decimal dosage_per_unit
        string unit
        string description
        datetime created_at
    }

    Take {
        int id PK
        int user_id FK
        int pharmaceutical_id FK
        decimal quantity
        datetime taken_at
        string notes
    }

    TakePlan {
        int id PK
        int user_id FK
        int pharmaceutical_id FK
        decimal dosage
        string unit
        int take_count
        datetime start_date
        datetime end_date
        boolean is_active
        string notes
        datetime created_at
    }

    User ||--o{ Take : "has_many"
    User ||--o{ TakePlan : "has_many"
    Pharmaceutical ||--o{ Take : "has_many"
    Pharmaceutical ||--o{ TakePlan : "has_many"
```

## 技術棧

### 後端技術
- **程式語言**: Python 3.13
- **Web 框架**: FastAPI
- **資料庫**: SQLite
- **ORM**: SQLModel
- **GraphQL**: Strawberry GraphQL
- **認證**: Python-JOSE (JWT)
- **密碼加密**: Passlib (bcrypt)
- **依賴管理**: uv

### 前端技術
- **框架**: Svelte 5.0
- **語言**: TypeScript
- **構建工具**: Vite
- **套件管理**: pnpm
- **開發工具**: SvelteKit

### 開發工具
- **測試框架**: pytest
- **資料生成**: Faker
- **設定格式**: YAML

## 核心功能模組

### 1. 使用者認證模組
- 檔案位置: `when_did_i_take/auth.py`, `when_did_i_take/graphql_auth.py`
- 功能: JWT 認證、密碼加密、權限驗證
- 認證流程: 登入 → 取得 JWT Token → 請求驗證

### 2. 資料模型模組
- 檔案位置: `when_did_i_take/models.py`
- 核心實體:
  - `User`: 使用者資訊
  - `Pharmaceutical`: 藥物資訊
  - `Take`: 服藥記錄
  - `TakePlan`: 服藥計劃

### 3. GraphQL API 模組
- 檔案位置: `when_did_i_take/graphql_schema.py`
- 提供查詢與變更操作
- 支援分頁、過濾、排序
- 整合認證機制

### 4. 資料庫管理模組
- 檔案位置: `when_did_i_take/db.py`
- 資料庫連接管理
- 資料表建立與初始化
- 支援 SQLite 檔案資料庫

## 部署架構

```mermaid
graph LR
    subgraph "開發環境"
        A1[Vite Dev Server :5173]
        A2[FastAPI Server :8000]
        A3[SQLite Database]
    end

    subgraph "生產環境 (規劃)"
        B1[Static Files]
        B2[API Gateway]
        B3[Application Server]
        B4[Database]
    end

    A1 --> A2
    A2 --> A3

    B1 --> B2
    B2 --> B3
    B3 --> B4
```

## 資料流程

```mermaid
sequenceDiagram
    participant F as 前端
    participant A as API Gateway
    participant G as GraphQL
    participant D as Database

    F->>A: 使用者登入
    A->>F: 回傳 JWT Token

    F->>G: GraphQL Query (附帶 Token)
    G->>G: 驗證 Token
    G->>D: 查詢資料
    D->>G: 回傳資料
    G->>F: 回傳 GraphQL 回應

    F->>G: GraphQL Mutation (建立記錄)
    G->>G: 驗證權限
    G->>D: 寫入資料
    D->>G: 確認結果
    G->>F: 回傳變更結果
```

## 安全性考量

### 認證與授權
- JWT Token 認證機制
- 使用者只能存取自己的資料
- 密碼使用 bcrypt 加密儲存

### 資料驗證
- GraphQL 輸入驗證
- 資料庫層面的約束檢查
- 使用者輸入清理

### 資料保護
- 敏感資料不納入版本控制
- 設定檔使用 `.env` 檔案
- 資料庫檔案適當權限設定

## 未來擴展規劃

### 短期目標
1. 完成前端使用者介面開發
2. 實作服藥提醒功能
3. 新增資料匯出功能

### 中期目標
1. 支援多種資料庫後端
2. 實作行動裝置 API
3. 新增資料分析功能

### 長期目標
1. 微服務架構重構
2. 雲端部署支援
3. 多租戶系統支援