# Caveats - 注意事項及血淚教訓

最後更新: 2025/7/21

## 知識
- Strawberry GraphQL 字段名稱轉換問題:Strawberry 自動將 Python snake_case 轉為 GraphQL camelCase，如 `created_by` → `createdBy`。
   撰寫前後端代碼時，應該再次確認API文件及model定義，以Query、mutation或物件、變數名稱有誤。
- 使用uv管理環境，因此執行自定義python腳本，應該使用`uv run mypyscript.py`，方能正確套用環境。
- 使用`Misc/dev-server.sh start`啟動前後端伺服器、`Misc/dev-server.sh stop`停止，`Misc/dev-server.sh check`檢查狀態，前後端port設定檔位於`Misc/.env.dev`。注意伺服器應該是以dev模式啟動的。
- 需要存取前後端進行驗證時，應該先使用`Misc/dev-server.sh check`檢查是否伺服器已經啟動，而不是直接嘗試啟動。系統中應該只保留一個運行中的副本。
- 前端程式位於`webapp`資料夾，後端程式位於`backend/when_did_i_take`資料夾。
- 測試資料庫檔案放在`backend/tests/test_data.db`，其中包含User, Pharmaceutical, Take等Schema。
- 測試資料庫中的User資料表，由於其密碼為加密，為了測試方便，任何使用者密碼統一使用`password123`。
- 若要查詢資料model定義，應該查詢`backend/when_did_i_take/models.py`及`backend/when_did_i_take/graphql_schema.py`。
- 生成測試資料時，必須要符合程式基本邏輯：
  - 每個使用者只能編輯自己建立的藥物
  - 每個使用者只能服用自己建立的藥物
- 當使用者提到「提供解決方案」，指不要直接修改代碼，只回覆預計採取的行動，等待進一步確認
- 前端採用混合模式：
  - ✅ 有使用Zod - 但僅限於認證相關的資料驗證
  - ✅ 已使用GraphQL客戶端框架 - 使用 urql 替代原生fetch
  - ⚠️ 部分直接解析 - GraphQL查詢結果大多直接解析為物件，缺乏運行時型別驗證
  - ✅ 有TypeScript型別定義 - 提供編譯時型別檢查

## 工作流程

### GraphQL API 變更流程
變更 API 必須按順序執行：

1. **修改後端** - 編輯 `backend/when_did_i_take/graphql_schema.py`
2. **導出 SDL** - `cd backend && uv run strawberry export-schema when_did_i_take.graphql_schema:schema -o ../Shared/api/graphql/schema.graphql`
3. **生成前端型別** - `cd webapp && pnpm run codegen`
4. **更新查詢** - 修改 `webapp/src/lib/graphql/queries/` 檔案
5. **更新業務邏輯** - 修改前端組件和服務
6. **更新測試** - 修改 `backend/tests/test_graphql_schema.py`

### 自動化提醒
- 修改 `graphql_schema.py` 後立即執行步驟 2-3
- 前端開發者 pull 代碼後執行 `pnpm run codegen`