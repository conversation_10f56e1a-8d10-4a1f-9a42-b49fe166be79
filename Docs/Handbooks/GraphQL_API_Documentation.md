# GraphQL API 文件

**版本**: 0.2.0
**最後更新**: 2025/7/16

---

## 概述

When Did I Take 用藥追蹤系統提供 GraphQL API 來管理用藥記錄、藥物資料和使用者資訊。所有 API 端點都需要 JWT 認證。

**GraphQL 端點**: `http://localhost:8001/graphql`

---

## 認證

所有 GraphQL 操作都需要在 HTTP Header 中包含 Bearer Token：

```
Authorization: Bearer YOUR_JWT_TOKEN
```

取得 JWT Token 請使用 REST 認證端點：
```
POST /auth/login
Content-Type: application/json

{
  "user_id": 1,
  "password": "password123"
}
```

---

## 資料型別

### 枚舉類型

#### FormulationType
藥物劑型枚舉：
```graphql
enum FormulationType {
  CAPSULE    # 膠囊
  PILL       # 藥丸
  INJECTION  # 注射劑
  POWDER     # 粉劑
  SYRUP      # 糖漿
  PATCH      # 貼片
  SPRAY      # 噴霧
  TOPICAL    # 外用藥
}
```

### 主要類型

#### UserType
使用者類型：
```graphql
type UserType {
  id: Int!
  name: String!
  email: String!
  takes: [TakeType!]!  # 此使用者的所有服藥記錄
}
```

#### PharmaceuticalType
藥物類型：
```graphql
type PharmaceuticalType {
  id: Int!
  name: String!
  formulation_type: FormulationType!
  dosage_per_unit: Float!
  unit: String!
  description: String
  created_by: Int!     # 建立此藥物的使用者ID
  takes: [TakeType!]!  # 此藥物的所有服藥記錄
}
```

#### TakeType
服藥記錄類型：
```graphql
type TakeType {
  id: Int!
  user_id: Int!
  pharmaceutical_id: Int!
  quantity: Float!
  taken_at: DateTime!
  notes: String
  user: UserType          # 服藥使用者資訊
  pharmaceutical: PharmaceuticalType  # 服藥藥物資訊
}
```

### 輸入類型

#### PharmaceuticalInput
建立藥物的輸入：
```graphql
input PharmaceuticalInput {
  name: String!
  formulation_type: FormulationType!
  dosage_per_unit: Float!
  unit: String!
  description: String
}
```

#### PharmaceuticalUpdateInput
更新藥物的輸入：
```graphql
input PharmaceuticalUpdateInput {
  id: Int!
  name: String
  formulation_type: FormulationType
  dosage_per_unit: Float
  unit: String
  description: String
}
```

#### TakeInput
建立服藥記錄的輸入：
```graphql
input TakeInput {
  user_id: Int!
  pharmaceutical_id: Int!
  quantity: Float!
  taken_at: DateTime!
  notes: String
}
```

---

## Query 操作

### 1. 取得服藥記錄

```graphql
query GetTakes(
  $userId: Int!,
  $pharmaceuticalId: Int,
  $fromTime: DateTime,
  $toTime: DateTime,
  $offset: Int = 0,
  $limit: Int = 10
) {
  getTakes(
    user_id: $userId,
    pharmaceutical_id: $pharmaceuticalId,
    from_time: $fromTime,
    to_time: $toTime,
    offset: $offset,
    limit: $limit
  ) {
    id
    user_id
    pharmaceutical_id
    quantity
    taken_at
    notes
    user {
      id
      name
      email
    }
    pharmaceutical {
      id
      name
      formulation_type
      dosage_per_unit
      unit
      description
    }
  }
}
```

**參數說明**：
- `user_id`: 使用者 ID（必填，只能查詢自己的記錄）
- `pharmaceutical_id`: 藥物 ID（選填，篩選特定藥物）
- `from_time`: 開始時間（選填）
- `to_time`: 結束時間（選填）
- `offset`: 分頁偏移量（預設：0）
- `limit`: 每頁筆數（預設：10）

**權限限制**：使用者只能查看自己的服藥記錄

### 2. 取得藥物列表

```graphql
query GetPharmaceuticals {
  getPharmaceuticals {
    id
    name
    formulation_type
    dosage_per_unit
    unit
    description
    created_by
    takes {
      id
      user_id
      quantity
      taken_at
      notes
    }
  }
}
```

**說明**：取得當前使用者建立的所有藥物資訊

**權限限制**：只能查看自己建立的藥物

### 3. 取得使用者列表

```graphql
query GetUsers {
  getUsers {
    id
    name
    email
    takes {
      id
      pharmaceutical_id
      quantity
      taken_at
      notes
    }
  }
}
```

**說明**：取得所有使用者的資訊

---

## Mutation 操作

### 1. 建立藥物

```graphql
mutation CreatePharmaceutical($pharmaceutical: PharmaceuticalInput!) {
  createPharmaceutical(pharmaceutical: $pharmaceutical) {
    id
    name
    formulation_type
    dosage_per_unit
    unit
    description
  }
}
```

**變數範例**：
```json
{
  "pharmaceutical": {
    "name": "阿斯匹靈",
    "formulation_type": "PILL",
    "dosage_per_unit": 325.0,
    "unit": "mg",
    "description": "解熱鎮痛藥"
  }
}
```

**限制**：
- 藥物名稱在同一使用者內必須唯一
- 藥物的 created_by 欄位會自動設定為當前使用者

### 2. 更新藥物

```graphql
mutation UpdatePharmaceutical($pharmaceutical: PharmaceuticalUpdateInput!) {
  updatePharmaceutical(pharmaceutical: $pharmaceutical) {
    id
    name
    formulation_type
    dosage_per_unit
    unit
    description
    created_by
  }
}
```

**變數範例**：
```json
{
  "pharmaceutical": {
    "id": 1,
    "name": "阿斯匹靈 300mg",
    "dosage_per_unit": 300.0,
    "description": "更新描述"
  }
}
```

**權限限制**：只能更新自己建立的藥物

### 3. 刪除藥物

```graphql
mutation DeletePharmaceutical($id: Int!) {
  deletePharmaceutical(id: $id)
}
```

**回傳值**：布林值，表示是否成功刪除

**限制**：
- 只能刪除自己建立的藥物
- 只能刪除沒有任何服用記錄的藥物。如果該藥物有服用記錄，將會拋出錯誤。

### 4. 建立服藥記錄

```graphql
mutation CreateTake($take: TakeInput!) {
  createTake(take: $take) {
    id
    user_id
    pharmaceutical_id
    quantity
    taken_at
    notes
    user {
      id
      name
      email
    }
    pharmaceutical {
      id
      name
      formulation_type
    }
  }
}
```

**變數範例**：
```json
{
  "take": {
    "user_id": 1,
    "pharmaceutical_id": 1,
    "quantity": 1.0,
    "taken_at": "2025-07-16T10:30:00",
    "notes": "早餐後服用"
  }
}
```

**權限限制**：
- 使用者只能為自己建立服藥記錄
- 只能使用自己建立的藥物建立服藥記錄

### 5. 刪除服藥記錄

```graphql
mutation DeleteTake($userId: Int!, $pharmaceuticalId: Int!, $takeId: Int!) {
  deleteTake(
    user_id: $userId,
    pharmaceutical_id: $pharmaceuticalId,
    take_id: $takeId
  )
}
```

**回傳值**：布林值，表示是否成功刪除

---

## 使用範例

### 完整的查詢範例

```graphql
query GetUserTakesWithDetails {
  getTakes(user_id: 1, limit: 5) {
    id
    quantity
    taken_at
    notes
    user {
      name
      email
    }
    pharmaceutical {
      name
      formulation_type
      dosage_per_unit
      unit
      description
    }
  }
}
```

### 完整的建立記錄範例

```graphql
mutation CreateTakeRecord {
  createTake(take: {
    user_id: 1,
    pharmaceutical_id: 1,
    quantity: 2.0,
    taken_at: "2025-07-16T08:00:00",
    notes: "早餐後服用，配溫水"
  }) {
    id
    quantity
    taken_at
    notes
    pharmaceutical {
      name
      dosage_per_unit
      unit
    }
  }
}
```

---

## 錯誤處理

### 常見錯誤

1. **認證失敗**
   ```json
   {
     "errors": [
       {
         "message": "無法驗證憑證",
         "extensions": {
           "code": "UNAUTHORIZED"
         }
       }
     ]
   }
   ```

2. **權限不足**
   ```json
   {
     "errors": [
       {
         "message": "只能查看自己的服藥記錄"
       }
     ]
   }
   ```

3. **資料驗證失敗**
   ```json
   {
     "errors": [
       {
         "message": "Pharmaceutical with name 'xxx' already exists"
       }
     ]
   }
   ```

4. **資源不存在**
   ```json
   {
     "errors": [
       {
         "message": "User with id 999 not found"
       }
     ]
   }
   ```

5. **刪除限制**
   ```json
   {
     "errors": [
       {
         "message": "Cannot delete pharmaceutical 'xxx' because it has existing take records"
       }
     ]
   }
   ```

6. **權限錯誤**
   ```json
   {
     "errors": [
       {
         "message": "You can only update pharmaceuticals you created"
       }
     ]
   }
   ```

   ```json
   {
     "errors": [
       {
         "message": "You can only delete pharmaceuticals you created"
       }
     ]
   }
   ```

   ```json
   {
     "errors": [
       {
         "message": "You can only create take records for pharmaceuticals you created"
       }
     ]
   }
   ```

---

## 安全性考量

1. **資料存取權限**：
   - 使用者只能查看和修改自己的服藥記錄
   - 使用者只能查看、修改和刪除自己建立的藥物
   - 使用者只能使用自己建立的藥物來建立服藥記錄
2. **JWT 認證**：所有操作都需要有效的 JWT Token
3. **資料驗證**：所有輸入資料都會進行驗證
4. **SQL 注入防護**：使用 SQLModel ORM 防止 SQL 注入攻擊
5. **藥物所有權**：每個藥物都有明確的建立者，確保資料隔離

---

## 開發工具

### GraphQL Playground
訪問 `http://localhost:8001/graphql` 可以使用 GraphQL Playground 進行 API 測試。

### 測試用戶資料
```
用戶 ID: 1, 密碼: password123, 姓名: 王宇軒
用戶 ID: 2, 密碼: password123, 姓名: 李淑敏
用戶 ID: 3, 密碼: password123, 姓名: 林麗娟
```

**注意**：測試環境中所有用戶的密碼都是 **`password123`**