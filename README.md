# When Did I Take - 用藥追蹤系統

一個用於追蹤個人用藥紀錄的全棧 Web 應用程式，採用 Python FastAPI 後端和 Svelte 前端，透過 GraphQL 進行資料交換。

## 專案架構

### 後端
- **框架**: Python FastAPI
- **資料庫**: SQLite with SQLModel ORM
- **API**: GraphQL (Strawberry)
- **認證**: JWT Token
- **開發工具**: uv (Python 包管理)

### 前端
- **框架**: Svelte 5 + SvelteKit
- **樣式**: TailwindCSS
- **狀態管理**: Svelte Stores
- **類型檢查**: TypeScript
- **建置工具**: Vite

## 功能特色

### 🔐 使用者認證
- 註冊/登入系統
- JWT Token 認證
- 使用者資料隔離

### 💊 藥物管理
- 建立個人藥物清單
- 藥物資訊管理（名稱、劑型、劑量、單位等）
- 藥物權限控制

### 📝 服藥紀錄
- 新增服藥紀錄
- 快速服藥功能
- 30分鐘內重複用藥警告
- 同一分鐘內禁止重複用藥（後端檢查）
- 服藥歷史查詢

### 🚀 智能功能
- 根據使用習慣自動生成快速服藥選項
- 分批載入30天內完整資料
- 深度比對 + 差異更新，避免不必要的重新渲染
- 變更動畫效果提升使用體驗

### 📱 使用者體驗
- 響應式設計，支援行動裝置
- 時區自動處理
- 即時資料更新
- 優雅的錯誤處理

## 開發伺服器管理

專案提供了便利的開發伺服器管理腳本 `dev-server.sh`，可以輕鬆管理前後端伺服器。

### 快速開始

```bash
# 檢查伺服器狀態
./dev-server.sh check

# 啟動開發伺服器（使用預設port）
./dev-server.sh start

# 停止伺服器
./dev-server.sh stop

# 顯示使用說明
./dev-server.sh help
```

### 伺服器管理功能

#### 1. 檢查伺服器狀態 (`check`)
```bash
./dev-server.sh check
```
- 檢查前後端伺服器是否運行中
- 顯示伺服器 PID 和佔用的 port
- 顯示環境變數中設定的 port
- 不依賴 port 佔用檢查，而是根據進程命令判斷

#### 2. 啟動開發伺服器 (`start`)
```bash
# 使用預設 port
./dev-server.sh start

# 指定後端 port
./dev-server.sh start 8080

# 指定前後端 port
./dev-server.sh start 8080 3000
```

**Port 決定優先順序:**
1. 命令列參數
2. 環境變數
3. 預設值

**預設 Port:**
- 後端: `8080, 8081, 8088`
- 前端: `58080, 58081, 58088`

**智能 Port 管理:**
- 自動檢查 port 是否被佔用
- 如果被本專案伺服器佔用，詢問是否重啟
- 如果被其他進程佔用，詢問是否使用下一個可用 port
- 自動儲存 port 設定到環境變數

**前端配置自動同步:**
- 檢查前端配置檔案中的API URL是否與後端port一致
- 如果不一致，自動詢問是否更新前端配置
- 支援 `jq` 或 `sed` 更新JSON配置檔案
- 確保前端能正確連接到後端API

**啟動特性:**
- 後端支援文件異動自動重新載入
- 前端使用 Vite 熱重載功能
- 自動安裝缺失的依賴
- 背景執行，日誌輸出到檔案

#### 3. 停止伺服器 (`stop`)
```bash
./dev-server.sh stop
```
- 自動找到運行中的專案伺服器
- 驗證 port 與環境變數是否一致
- 如果不一致，會提示使用者確認
- 優雅停止，必要時強制終止

### 環境變數管理

腳本會自動管理環境變數檔案 `.env.dev`：

```bash
# 範例內容
BACKEND_PORT=8080
FRONTEND_PORT=58080
```

### 前端配置管理

前端使用動態配置系統，配置檔案位於 `webapp/static/config.json`：

```json
{
  "API_BASE_URL": "http://localhost:8080",
  "API_TIMEOUT": 10000,
  "APP_NAME": "When Did I Take",
  "APP_VERSION": "1.0.0"
}
```

**特點:**
- 前端會自動載入此配置檔案
- 開發腳本會檢查API URL與後端port是否一致
- 支援動態更新，無需重新建置前端應用

### 日誌檔案

- 後端日誌: `backend.log`
- 前端日誌: `frontend.log`

## 手動安裝與執行

### 後端設定

1. **安裝 Python 依賴**
   ```bash
   # 使用 uv (推薦)
   uv sync
   
   # 或使用傳統方式
   python -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

2. **啟動後端伺服器**
   ```bash
   # 使用 uv (推薦)
   uv run uvicorn when_did_i_take.api.main:app --host 0.0.0.0 --port 8080 --reload
   
   # 或傳統方式
   uvicorn when_did_i_take.api.main:app --host 0.0.0.0 --port 8080 --reload
   ```

3. **後端服務地址**
   - API: `http://localhost:8080`
   - GraphQL Playground: `http://localhost:8080/graphql`
   - 健康檢查: `http://localhost:8080/health`

### 前端設定

1. **安裝 Node.js 依賴**
   ```bash
   cd webapp
   
   # 使用 pnpm (推薦)
   pnpm install
   
   # 或使用 npm
   npm install
   ```

2. **啟動前端伺服器**
   ```bash
   # 使用 pnpm
   pnpm dev
   
   # 或使用 npm
   npm run dev
   ```

3. **前端應用地址**
   - 應用: `http://localhost:5173`

### 建置生產版本

```bash
# 前端建置
cd webapp
pnpm build

# 預覽建置結果
pnpm preview
```

## 開發指南

### 技術規範

請參閱以下文檔：
- [協作規範](Docs/CollaborationGuild.md) - 開發流程和代碼規範
- [技術陷阱](Docs/Caveats.md) - 已知問題和解決方案
- [API 文檔](Docs/Handbooks/GraphQL_API_Documentation.md) - GraphQL API 參考

### 關鍵開發原則

1. **時區處理**: 所有時間都使用本地時區處理
2. **權限控制**: 使用者只能操作自己的資料
3. **資料一致性**: 深度比對避免不必要的更新
4. **使用者體驗**: 30分鐘警告、動畫效果、即時回饋

### 測試

```bash
# 後端測試
uv run pytest

# 前端測試
cd webapp
pnpm test
```

## 故障排除

### 常見問題

1. **後端啟動失敗**
   - 檢查 Python 環境和依賴
   - 查看 `backend.log` 錯誤訊息
   - 確認 port 未被佔用

2. **前端啟動失敗**
   - 檢查 Node.js 版本 (需要 18+)
   - 重新安裝依賴: `rm -rf node_modules && pnpm install`
   - 查看 `frontend.log` 錯誤訊息

3. **GraphQL 錯誤**
   - 檢查認證 token 是否有效
   - 確認後端服務正常運行
   - 查看瀏覽器開發者工具網路請求

4. **資料庫問題**
   - 資料庫檔案位於 `test_data.db`
   - 如需重置，刪除檔案後重啟後端

### 除錯工具

- **GraphQL Playground**: `http://localhost:8080/graphql`
- **瀏覽器開發者工具**: 網路、控制台標籤
- **日誌檔案**: `backend.log`, `frontend.log`

## 版本資訊

- **當前版本**: v1.0.0
- **最後更新**: 2025/07/19

## 貢獻

1. Fork 專案
2. 建立功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交變更 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

## 授權

此專案為私人開發專案。

---

**開發愉快！** 🎉

如有問題，請查看日誌檔案或聯繫開發團隊。