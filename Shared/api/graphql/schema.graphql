"""Date with time (isoformat)"""
scalar DateTime

enum FormulationType {
  CAPSULE
  PILL
  INJECTION
  POWDER
  SYRUP
  PATCH
  SPRAY
  TOPICAL
}

type Mutation {
  createPharmaceutical(pharmaceutical: PharmaceuticalInput!): PharmaceuticalType!
  updatePharmaceutical(pharmaceutical: PharmaceuticalUpdateInput!): PharmaceuticalType!
  deletePharmaceutical(id: Int!): Boolean!
  createTake(take: TakeInput!): TakeType!
  updateTake(take: TakeUpdateInput!): TakeType!
  deleteTake(takeId: Int!): Boolean!
}

input PharmaceuticalInput {
  name: String!
  formulationType: FormulationType!
  dosagePerServing: Float!
  dosageUnit: String!
  servingUnitName: String = null
  currentPlan: Boolean = true
  description: String = null
}

type PharmaceuticalType {
  id: Int!
  name: String!
  formulationType: FormulationType!
  dosagePerServing: Float!
  dosageUnit: String!
  servingUnitName: String
  currentPlan: Boolean!
  description: String
  createdBy: Int!
  takes: [TakeType!]!
}

input PharmaceuticalUpdateInput {
  id: Int!
  name: String = null
  formulationType: FormulationType = null
  dosagePerServing: Float = null
  dosageUnit: String = null
  servingUnitName: String = null
  currentPlan: Boolean = null
  description: String = null
}

type Query {
  getTakes(pharmaceuticalId: Int = null, fromTime: DateTime = null, toTime: DateTime = null, offset: Int! = 0, limit: Int! = 10): [TakeType!]!
  getPharmaceuticals(currentPlanOnly: Boolean = null): [PharmaceuticalType!]!
  getUser: UserType!
}

input TakeInput {
  userId: Int!
  pharmaceuticalId: Int!
  quantity: Float!
  takenAt: DateTime!
  notes: String = null
}

type TakeType {
  id: Int!
  userId: Int!
  pharmaceuticalId: Int!
  quantity: Float!
  takenAt: DateTime!
  notes: String
  user: UserType
  pharmaceutical: PharmaceuticalType
}

input TakeUpdateInput {
  id: Int!
  quantity: Float = null
  takenAt: DateTime = null
  notes: String = null
}

type UserType {
  id: Int!
  name: String!
  email: String!
  takes: [TakeType!]!
}
