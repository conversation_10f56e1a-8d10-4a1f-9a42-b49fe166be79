overwrite: true
schema: '../Shared/api/graphql/schema.graphql'
generates:
  ./src/lib/graphql/generated.ts:
    plugins:
      - typescript
    config:
      # TypeScript 配置
      enumsAsTypes: true
      constEnums: true
      numericEnums: false
      
      # 命名配置
      typesPrefix: 'GraphQL'
      typesSuffix: ''
      enumPrefix: false
      enumSuffix: 'Enum'
      
      # 輸出配置
      declarationKind: 'interface'
      maybeValue: 'T | null | undefined'
      inputMaybeValue: 'T | null | undefined'
      
      # 標量類型映射
      scalars:
        DateTime: 'string'
        
  ./src/lib/graphql/schema.json:
    plugins:
      - introspection
    config:
      minify: true