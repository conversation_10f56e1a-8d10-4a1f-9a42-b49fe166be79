{"name": "webapp", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "codegen": "graphql-codegen --config codegen-simple.yml", "codegen:watch": "graphql-codegen --config codegen-simple.yml --watch"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/introspection": "^4.0.3", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-tools/graphql-file-loader": "^8.0.22", "@headlessui/tailwindcss": "^0.2.2", "@heroicons/react": "^2.2.0", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^4.0.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.0.0", "@testing-library/svelte": "^5.0.0", "@testing-library/user-event": "^14.0.0", "@types/js-cookie": "^3.0.6", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "jsdom": "^25.0.0", "lucide-svelte": "^0.525.0", "msw": "^2.0.0", "postcss": "^8.5.6", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "typescript": "^5.0.0", "vite": "^5.0.3", "vitest": "^2.0.0"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"], "ignoredBuiltDependencies": ["@tailwindcss/oxide"]}, "dependencies": {"@urql/exchange-auth": "^2.2.1", "@urql/svelte": "^4.2.3", "date-fns": "^4.1.0", "graphql": "^16.11.0", "js-cookie": "^3.0.5", "zod": "^4.0.5"}}