<script lang="ts">
  import { goto } from '$app/navigation';
  import { login, saveToken, type LoginRequest } from '$lib/auth';
  import { rememberMeService } from '$lib/services/rememberMeService';
  import { dataManager } from '$lib/services/dataManager';
  import { onMount } from 'svelte';

  let userName = '';
  let password = '';
  let isLoading = false;
  let errorMessage = '';
  let rememberMe = false; // 記住我選項

  // 密碼顯示控制
  let showPassword = false; // 預設隱藏

  onMount(() => {
    // 清除之前的錯誤訊息
    errorMessage = '';
    
    // 載入記住的登入資訊
    loadRememberedCredentials();
  });

  function loadRememberedCredentials() {
    const remembered = rememberMeService.load();
    if (remembered && remembered.rememberMe) {
      userName = remembered.userName || '';
      rememberMe = true;
    }
  }

  function saveRememberedCredentials() {
    rememberMeService.save({
      userName: userName,
      rememberMe: rememberMe
    });
  }

  async function handleLogin() {
    if (!userName || !password) {
      errorMessage = '請輸入使用者名稱和密碼';
      return;
    }

    // 檢查 userName 是否為數字
    const userId = parseInt(userName);
    if (isNaN(userId)) {
      errorMessage = '使用者名稱必須是數字';
      return;
    }

    isLoading = true;
    errorMessage = '';

    try {
      const credentials: LoginRequest = {
        user_id: userId,
        password
      };

      const response = await login(credentials);

      // 儲存 JWT
      saveToken(response.access_token);

      // 保存記住的登入資訊
      saveRememberedCredentials();

      // 開始載入基礎資料（在背景進行）
      console.log('登入成功，開始載入使用者資料...');
      dataManager.loadUserInfo().then(() => {
        console.log('使用者資料載入成功');
      }).catch(error => {
        console.error('載入使用者資料失敗:', error);
      });
      
      dataManager.loadPharmaceuticals().then(() => {
        console.log('藥物清單載入成功');
      }).catch(error => {
        console.error('載入藥物清單失敗:', error);
      });
      
      dataManager.loadTakeRecords().then(() => {
        console.log('服藥記錄載入成功');
      }).catch(error => {
        console.error('載入服藥記錄失敗:', error);
      });

      // 立即跳轉到主頁面
      goto('/whendiditake');
    } catch (error) {
      if (error instanceof Error) {
        errorMessage = error.message;
      } else {
        errorMessage = '登入失敗，請稍後重試';
      }
    } finally {
      isLoading = false;
    }
  }

  function handleKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      handleLogin();
    }
  }

  // 切換密碼顯示/隱藏
  function togglePasswordVisibility() {
    showPassword = !showPassword;
  }
</script>

<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 via-secondary-50 to-surface-50 dark:from-surface-900 dark:via-primary-900 dark:to-secondary-900 relative overflow-hidden">
  <!-- 背景裝飾 -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute top-20 left-20 w-72 h-72 bg-primary-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
    <div class="absolute top-40 right-20 w-72 h-72 bg-secondary-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"></div>
    <div class="absolute -bottom-8 left-1/2 w-72 h-72 bg-primary-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-500"></div>
  </div>

  <div class="relative z-10 w-full max-w-md mx-4">
    <!-- 主卡片 -->
    <div class="bg-white/80 dark:bg-gray-800/90 backdrop-blur-lg rounded-2xl shadow-2xl border border-white/20 dark:border-gray-700/50 p-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <div class="mb-6 relative">
          <div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-secondary-600 rounded-2xl mx-auto flex items-center justify-center shadow-lg">
            <iconify-icon icon="lucide:pill" class="text-3xl text-white"></iconify-icon>
          </div>
          <div class="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full animate-ping"></div>
          <div class="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full"></div>
        </div>
        <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
          When Did I Take
        </h1>
        <p class="text-gray-600 dark:text-gray-300 text-sm">安全登入您的用藥管理系統</p>
      </div>

      <!-- Login Form -->
      <div class="space-y-6">
        <!-- Username Field -->
        <div class="space-y-2">
          <label for="username" class="text-sm font-medium text-gray-700 dark:text-gray-300 block">
            使用者 ID
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <iconify-icon icon="lucide:user" class="text-gray-400 text-lg"></iconify-icon>
            </div>
            <input
              id="username"
              type="text"
              class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50"
              placeholder="請輸入使用者 ID"
              bind:value={userName}
              on:keypress={handleKeyPress}
              disabled={isLoading}
              autocomplete="username"
            />
          </div>
        </div>

        <!-- Password Field -->
        <div class="space-y-2">
          <label for="password" class="text-sm font-medium text-gray-700 dark:text-gray-300 block">
            密碼
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <iconify-icon icon="lucide:lock" class="text-gray-400 text-lg"></iconify-icon>
            </div>
            <input
              id="password"
              type={showPassword ? "text" : "password"}
              class="w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50"
              placeholder="請輸入密碼"
              bind:value={password}
              on:keypress={handleKeyPress}
              disabled={isLoading}
              autocomplete="current-password"
            />
            <button
              type="button"
              class="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-blue-500 transition-colors duration-200"
              on:click={togglePasswordVisibility}
              disabled={isLoading}
              aria-label={showPassword ? "隱藏密碼" : "顯示密碼"}
            >
              <iconify-icon
                icon={showPassword ? "lucide:eye-off" : "lucide:eye"}
                class="text-gray-400 hover:text-blue-500 text-lg"
              ></iconify-icon>
            </button>
          </div>
        </div>

        <!-- Remember Me Checkbox -->
        <div class="flex items-center space-x-3">
          <div class="flex items-center">
            <input
              id="rememberMe"
              type="checkbox"
              bind:checked={rememberMe}
              class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              disabled={isLoading}
            />
            <label for="rememberMe" class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
              記住我
            </label>
          </div>
          <div class="flex-1"></div>
          <button
            type="button"
            class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors duration-200"
            disabled={isLoading}
          >
            忘記密碼？
          </button>
        </div>

        <!-- Error Message -->
        {#if errorMessage}
          <div class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-xl p-4 flex items-center space-x-3">
            <div class="flex-shrink-0">
              <iconify-icon icon="lucide:alert-circle" class="text-red-500 text-xl"></iconify-icon>
            </div>
            <div class="flex-1">
              <p class="text-red-700 dark:text-red-300 text-sm font-medium">{errorMessage}</p>
            </div>
          </div>
        {/if}

        <!-- Login Button -->
        <button
          class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2"
          disabled={isLoading}
          on:click={handleLogin}
        >
          {#if isLoading}
            <iconify-icon icon="lucide:loader-2" class="text-xl animate-spin"></iconify-icon>
            <span>登入中...</span>
          {:else}
            <iconify-icon icon="lucide:log-in" class="text-xl"></iconify-icon>
            <span>登入</span>
          {/if}
        </button>
      </div>
    </div>

    <!-- 浮動提示 -->
    <div class="mt-6 text-center">
      <div class="inline-flex items-center space-x-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-4 py-2 rounded-full text-xs font-medium">
        <iconify-icon icon="lucide:info" class="text-sm"></iconify-icon>
        <span>使用您的用戶ID和密碼登入</span>
      </div>
    </div>
  </div>
</div>

