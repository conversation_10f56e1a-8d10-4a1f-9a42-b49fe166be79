<script>
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { queryPharmaceuticals, deletePharmaceutical } from '$lib/graphql/services';
  import Navigation from '$lib/components/Navigation.svelte';

  let pharmaceuticals = [];
  let loading = true;
  let error = null;

  const formulations = {
    'CAPSULE': '膠囊',
    'PILL': '藥丸',
    'INJECTION': '注射劑',
    'POWDER': '粉劑',
    'SYRUP': '糖漿',
    'PATCH': '貼片',
    'SPRAY': '噴霧',
    'TOPICAL': '外用藥'
  };

  onMount(async () => {
    await loadPharmaceuticals();
  });

  async function loadPharmaceuticals() {
    try {
      loading = true;
      error = null;
      
      pharmaceuticals = await queryPharmaceuticals();
    } catch (err) {
      error = err.message;
      console.error('載入藥物清單失敗:', err);
    } finally {
      loading = false;
    }
  }

  async function deletePharmaeutical(id, name) {
    if (!confirm(`確定要刪除藥物「${name}」嗎？`)) {
      return;
    }

    try {
      const success = await deletePharmaceutical({ id });

      if (success) {
        await loadPharmaceuticals();
      }
    } catch (err) {
      alert(`刪除失敗：${err.message}`);
      console.error('刪除藥物失敗:', err);
    }
  }

  function goToNewPharmaceutical() {
    goto('/pharmaceuticals/new');
  }
</script>

<div class="min-h-screen bg-surface-50 dark:bg-surface-900">
  <Navigation />

  <main class="container mx-auto px-4 pt-20 pb-8">
    <div class="flex justify-between items-center mb-8">
      <h1 class="h1 text-surface-900 dark:text-surface-100">藥物管理</h1>
      <button 
        class="btn btn-primary"
        on:click={goToNewPharmaceutical}
      >
        <iconify-icon icon="lucide:plus" class="text-lg"></iconify-icon>
        <span>新增藥物</span>
      </button>
    </div>

    {#if loading}
      <div class="flex justify-center items-center h-64">
        <div class="flex items-center gap-3">
          <iconify-icon icon="lucide:loader-2" class="text-3xl text-primary-600 animate-spin"></iconify-icon>
          <span class="text-surface-600 dark:text-surface-300">載入中...</span>
        </div>
      </div>
    {:else if error}
      <div class="alert alert-error">
        <iconify-icon icon="lucide:alert-circle" class="text-xl"></iconify-icon>
        <span>載入失敗：{error}</span>
      </div>
    {:else if pharmaceuticals.length === 0}
      <div class="text-center py-16">
        <iconify-icon icon="lucide:pill" class="text-6xl text-surface-400 mb-4"></iconify-icon>
        <h3 class="h3 text-surface-600 dark:text-surface-300 mb-4">尚無藥物資料</h3>
        <p class="text-surface-500 dark:text-surface-400 mb-6">開始建立您的藥物資料庫</p>
        <button 
          class="btn btn-primary"
          on:click={goToNewPharmaceutical}
        >
          <iconify-icon icon="lucide:plus" class="text-lg"></iconify-icon>
          <span>新增第一個藥物</span>
        </button>
      </div>
    {:else}
      <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {#each pharmaceuticals as pharmaceutical}
          <div class="card p-6 hover:shadow-xl transition-shadow duration-200">
            <div class="flex items-start justify-between mb-4">
              <h3 class="h3 text-surface-800 dark:text-surface-100">{pharmaceutical.name}</h3>
              <iconify-icon icon="lucide:pill" class="text-xl text-primary-500"></iconify-icon>
            </div>
            
            <div class="space-y-3">
              <div class="badge badge-success">
                {formulations[pharmaceutical.formulationType]}
              </div>
              
              <div class="text-sm text-surface-600 dark:text-surface-300">
                <span class="font-medium">每份劑量：</span>
                <span class="font-semibold text-primary-600">{pharmaceutical.dosagePerServing} {pharmaceutical.dosageUnit}</span>
                {#if pharmaceutical.servingUnitName}
                  <span class="text-surface-500">({pharmaceutical.servingUnitName})</span>
                {/if}
              </div>

              <div class="flex items-center gap-2 text-sm">
                <span class="font-medium text-surface-600 dark:text-surface-300">狀態：</span>
                {#if pharmaceutical.currentPlan}
                  <div class="badge badge-success">治療中</div>
                {:else}
                  <div class="badge badge-secondary">非治療中</div>
                {/if}
              </div>
              
              {#if pharmaceutical.description}
                <div class="text-sm text-surface-600 dark:text-surface-300">
                  <span class="font-medium">描述：</span>
                  <span>{pharmaceutical.description}</span>
                </div>
              {/if}
              
              <div class="flex items-center gap-2 text-sm text-surface-500 dark:text-surface-400">
                <iconify-icon icon="lucide:history" class="text-sm"></iconify-icon>
                <span>服用記錄：{pharmaceutical.takes.length} 次</span>
              </div>
            </div>
            
            <div class="flex justify-end mt-6">
              <button 
                class="btn btn-error"
                class:opacity-50={pharmaceutical.takes.length > 0}
                on:click={() => deletePharmaeutical(pharmaceutical.id, pharmaceutical.name)}
                disabled={pharmaceutical.takes.length > 0}
                title={pharmaceutical.takes.length > 0 ? "有服用記錄，無法刪除" : "刪除藥物"}
              >
                <iconify-icon icon="lucide:trash-2" class="text-sm"></iconify-icon>
                <span>{pharmaceutical.takes.length > 0 ? '無法刪除' : '刪除'}</span>
              </button>
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </main>
</div>