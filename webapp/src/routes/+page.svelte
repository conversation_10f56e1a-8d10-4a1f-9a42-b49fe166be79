<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { checkAuthStatus } from '$lib/auth';

  onMount(async () => {
    const isAuth = await checkAuthStatus();
    
    if (isAuth) {
      goto('/whendiditake');
    } else {
      goto('/login');
    }
  });
</script>

<div class="min-h-screen flex items-center justify-center bg-base-200">
  <div class="loading loading-spinner loading-lg"></div>
</div>
