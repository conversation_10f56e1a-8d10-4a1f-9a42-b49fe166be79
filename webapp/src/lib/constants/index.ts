/**
 * 應用程式常數定義
 * 最後更新: 2025/7/22
 */

// 時間相關常數
export const TIME_CONSTANTS = {
  // 毫秒
  ONE_SECOND: 1000,
  ONE_MINUTE: 60 * 1000,
  ONE_HOUR: 60 * 60 * 1000,
  ONE_DAY: 24 * 60 * 60 * 1000,
  
  // 業務邏輯時間
  RECENT_TAKE_WARNING_MINUTES: 30,
  TOKEN_REFRESH_CHECK_INTERVAL: 300000, // 5分鐘
  USER_ACTIVITY_THROTTLE_INTERVAL: 30000, // 30秒
  DATA_CACHE_TTL: 10 * 60 * 1000, // 10分鐘
  
  // JWT 相關
  JWT_REFRESH_THRESHOLD_SECONDS: 290, // 290秒
  JWT_MAX_LIFETIME_HOURS: 11.5,
  USER_INACTIVITY_LIMIT_MINUTES: 10,
} as const;

// API 相關常數
export const API_CONSTANTS = {
  DEFAULT_TIMEOUT: 10000,
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  
  // 分頁
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // GraphQL
  GRAPHQL_ENDPOINT: '/graphql/',
} as const;

// UI 相關常數
export const UI_CONSTANTS = {
  // 動畫時間
  THEME_TRANSITION_DURATION: 300,
  LOADING_DEBOUNCE_DELAY: 200,
  TOAST_DURATION: 3000,
  
  // 響應式斷點 (與 Tailwind 一致)
  BREAKPOINTS: {
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
    '2XL': 1536,
  },
  
  // Z-index 層級
  Z_INDEX: {
    DROPDOWN: 1000,
    MODAL: 1050,
    TOAST: 1100,
    TOOLTIP: 1150,
  },
} as const;

// 本地儲存鍵名
export const STORAGE_KEYS = {
  JWT_TOKEN: 'jwt_token',
  THEME: 'theme',
  USER_PREFERENCES: 'user_preferences',
  REMEMBER_ME: 'remember_me',
} as const;

// 錯誤訊息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '網路連線失敗，請檢查網路狀態',
  AUTH_EXPIRED: '登入已過期，請重新登入',
  PERMISSION_DENIED: '權限不足，無法執行此操作',
  VALIDATION_FAILED: '資料驗證失敗，請檢查輸入內容',
  SERVER_ERROR: '伺服器發生錯誤，請稍後再試',
  UNKNOWN_ERROR: '發生未知錯誤',
  
  // 業務邏輯錯誤
  PHARMACEUTICAL_NOT_FOUND: '指定的藥物不存在',
  RECENT_TAKE_WARNING: '您在30分鐘內已服用過此藥物',
  DUPLICATE_TAKE_ERROR: '同一分鐘內不能重複服藥',
} as const;

// 成功訊息
export const SUCCESS_MESSAGES = {
  TAKE_ADDED: '服藥記錄已新增',
  TAKE_UPDATED: '服藥記錄已更新',
  TAKE_DELETED: '服藥記錄已刪除',
  PHARMACEUTICAL_ADDED: '藥物已新增',
  PHARMACEUTICAL_UPDATED: '藥物已更新',
  PHARMACEUTICAL_DELETED: '藥物已刪除',
  LOGIN_SUCCESS: '登入成功',
  LOGOUT_SUCCESS: '已安全登出',
} as const;

// 表單驗證規則
export const VALIDATION_RULES = {
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_MAX_LENGTH: 128,
  USERNAME_MIN_LENGTH: 3,
  USERNAME_MAX_LENGTH: 50,
  PHARMACEUTICAL_NAME_MAX_LENGTH: 100,
  NOTES_MAX_LENGTH: 500,
  
  // 數值範圍
  MIN_DOSAGE: 0.001,
  MAX_DOSAGE: 99999,
  MIN_QUANTITY: 0.001,
  MAX_QUANTITY: 99999,
} as const;

// 藥物相關常數
export const PHARMACEUTICAL_CONSTANTS = {
  DEFAULT_DOSAGE_UNIT: 'mg',
  DEFAULT_SERVING_UNIT: '份',
  
  // 劑型選項
  FORMULATION_TYPES: [
    'Capsule',
    'Pill',
    'Injection',
    'Powder',
    'Syrup',
    'Patch',
    'Spray',
    'Topical'
  ],
  
  // 常用劑量單位
  DOSAGE_UNITS: [
    'mg',
    'g',
    'ml',
    'mcg',
    'IU',
    'unit'
  ],
  
  // 常用服用單位
  SERVING_UNITS: [
    '份',
    '顆',
    '粒',
    '錠',
    '包',
    '瓶',
    '支',
    '滴'
  ],
} as const;

// 主題相關常數
export const THEME_CONSTANTS = {
  THEMES: ['light', 'dark', 'auto'] as const,
  DEFAULT_THEME: 'auto' as const,
  STORAGE_KEY: 'theme',
} as const;

// 路由常數
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  MAIN: '/whendiditake',
  PHARMACEUTICALS: '/pharmaceuticals',
  NEW_PHARMACEUTICAL: '/pharmaceuticals/new',
} as const;

// 導出所有常數的聯合類型
export type TimeConstants = typeof TIME_CONSTANTS;
export type ApiConstants = typeof API_CONSTANTS;
export type UiConstants = typeof UI_CONSTANTS;
export type StorageKeys = typeof STORAGE_KEYS;
export type ErrorMessages = typeof ERROR_MESSAGES;
export type SuccessMessages = typeof SUCCESS_MESSAGES;
export type ValidationRules = typeof VALIDATION_RULES;
export type PharmaceuticalConstants = typeof PHARMACEUTICAL_CONSTANTS;
export type ThemeConstants = typeof THEME_CONSTANTS;
export type Routes = typeof ROUTES;
