/**
 * 藥物相關型別定義 - 以 GraphQL 生成型別為基礎，擴展業務邏輯型別
 * 最後更新: 2025/7/22
 */

import type { 
  GraphQLPharmaceuticalType, 
  GraphQLTakeType, 
  GraphQLTakeInput,
  GraphQLTakeUpdateInput,
  FormulationType
} from '$lib/graphql/generated';

// === 基礎型別 (基於 GraphQL 生成型別) ===

// 基礎藥物資訊 - 直接使用 GraphQL 型別
export type Pharmaceutical = GraphQLPharmaceuticalType;

// 可用藥物資訊 - 為了向後相容，建立別名和轉換
export interface AvailablePharmaceutical {
  id: number;
  name: string;
  unit: string; // 映射 dosageUnit
  dosagePerServing?: number;
  servingUnitName?: string;
  currentPlan?: boolean;
  description?: string;
}

// 服藥記錄 - 擴展 GraphQL 型別，加入業務邏輯欄位
export interface PharmaceuticalTakeRecord extends Omit<GraphQLTakeType, 'pharmaceutical' | 'user' | '__typename'> {
  pharmaceuticalName: string; // 業務邏輯所需
  dosage: number; // 別名 quantity，保持向後相容
  dosageUnit: string;
  dosagePerServing: number;
  servingUnitName?: string | null;
  createdBy: number;
}

// 新增服藥記錄輸入 - 直接使用 GraphQL 型別
export type AddPharmaceuticalTakeInput = GraphQLTakeInput;

// 更新服藥記錄輸入 - 直接使用 GraphQL 型別
export type UpdatePharmaceuticalTakeInput = GraphQLTakeUpdateInput;

// === 純業務邏輯型別 (GraphQL 中不存在) ===

// 快速服藥選項 - 純業務邏輯概念
export interface QuickPharmaceuticalOption {
  pharmaceuticalId: number;
  pharmaceuticalName: string;
  dosage: number;
  dosageUnit: string;
  count: number; // 該劑量的總服用次數
  todayCount: number; // 當日服用次數
  lastTakenAt: string; // 最後服用時間
}

// 服藥統計資訊 - 業務計算結果
export interface PharmaceuticalStats {
  pharmaceuticalId: number;
  pharmaceuticalName: string;
  totalTakes: number;
  todayTakes: number;
  weeklyTakes: number;
  monthlyTakes: number;
  lastTakenAt?: string;
  averageDailyDosage: number;
}

// 服藥提醒設定 - 未來功能的業務概念
export interface PharmaceuticalReminder {
  id: number;
  pharmaceuticalId: number;
  userId: number;
  reminderTimes: string[]; // 提醒時間陣列 (HH:MM 格式)
  isActive: boolean;
  notes?: string;
}

// === 重新匯出 GraphQL 型別 (避免重複定義) ===

// 劑型枚舉 - 直接重新匯出 GraphQL 型別
export type { FormulationType } from '$lib/graphql/generated';

// 服藥頻率枚舉 - 純業務邏輯，GraphQL 中不存在
export enum DosageFrequency {
  ONCE_DAILY = 'once_daily',
  TWICE_DAILY = 'twice_daily',
  THREE_TIMES_DAILY = 'three_times_daily',
  FOUR_TIMES_DAILY = 'four_times_daily',
  AS_NEEDED = 'as_needed',
  CUSTOM = 'custom'
}

// === 型別轉換函式 ===

/**
 * 將 GraphQL Pharmaceutical 轉換為 AvailablePharmaceutical
 */
export function toAvailablePharmaceutical(pharmaceutical: GraphQLPharmaceuticalType): AvailablePharmaceutical {
  return {
    id: pharmaceutical.id,
    name: pharmaceutical.name,
    unit: pharmaceutical.dosageUnit,
    dosagePerServing: pharmaceutical.dosagePerServing,
    servingUnitName: pharmaceutical.servingUnitName || undefined,
    currentPlan: pharmaceutical.currentPlan,
    description: pharmaceutical.description || undefined,
  };
}

/**
 * 將 GraphQL Take 轉換為 PharmaceuticalTakeRecord
 */
export function toPharmaceuticalTakeRecord(
  take: GraphQLTakeType, 
  pharmaceuticalName: string,
  dosageUnit: string,
  dosagePerServing: number,
  servingUnitName?: string | null,
  createdBy?: number
): PharmaceuticalTakeRecord {
  return {
    id: take.id,
    pharmaceuticalId: take.pharmaceuticalId,
    userId: take.userId,
    quantity: take.quantity,
    takenAt: take.takenAt,
    notes: take.notes || undefined,
    
    // 業務邏輯欄位
    pharmaceuticalName,
    dosage: take.quantity, // 別名
    dosageUnit,
    dosagePerServing,
    servingUnitName,
    createdBy: createdBy || take.userId,
  };
}