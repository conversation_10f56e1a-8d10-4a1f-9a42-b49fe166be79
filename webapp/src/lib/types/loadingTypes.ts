/**
 * 載入狀態定義
 * 最後更新: 2025/7/21
 */

export enum LoadingState {
  /** 尚未載入 - 初始狀態，沒有資料也沒有載入 */
  NOT_LOADED = 'not_loaded',
  /** 載入中 - 正在進行首次載入 */
  LOADING = 'loading',
  /** 已載入 - 資料載入完成且可用 */
  LOADED = 'loaded',
  /** 重新載入中 - 有舊資料但正在重新載入 */
  REFRESHING = 'refreshing',
  /** 載入錯誤 - 載入失敗且沒有可用資料 */
  ERROR = 'error',
  /** 載入錯誤但有快取 - 載入失敗但有舊資料可用 */
  ERROR_WITH_CACHE = 'error_with_cache'
}

export interface LoadingStatus {
  state: LoadingState;
  error?: string;
  lastUpdate?: Date;
  lastAttempt?: Date;
  retryCount?: number;
}

export interface DataState<T> {
  data: T | null;
  loading: LoadingStatus;
}

/**
 * 檢查是否正在載入中
 */
export function isLoading(state: LoadingState): boolean {
  return state === LoadingState.LOADING || state === LoadingState.REFRESHING;
}

/**
 * 檢查是否有可用資料
 */
export function hasData(state: LoadingState): boolean {
  return state === LoadingState.LOADED || 
         state === LoadingState.REFRESHING || 
         state === LoadingState.ERROR_WITH_CACHE;
}

/**
 * 檢查是否有錯誤
 */
export function hasError(state: LoadingState): boolean {
  return state === LoadingState.ERROR || state === LoadingState.ERROR_WITH_CACHE;
}

/**
 * 檢查是否需要顯示載入指示器
 */
export function shouldShowLoader(state: LoadingState): boolean {
  return state === LoadingState.LOADING;
}

/**
 * 檢查是否需要顯示骨架屏或佔位元素
 */
export function shouldShowSkeleton(state: LoadingState): boolean {
  return state === LoadingState.NOT_LOADED || state === LoadingState.LOADING;
}