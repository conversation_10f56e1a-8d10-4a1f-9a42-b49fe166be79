import { browser } from '$app/environment';
import { updateUserActivity } from './auth';

/**
 * 全域使用者活動追蹤模組
 * 自動監聽使用者互動並更新活動時間戳記
 * 包含節流機制避免過度頻繁的更新
 */

// 節流配置
const THROTTLE_INTERVAL = 30000; // 30秒
let lastUpdateTime = 0;
let throttledUpdateTimeout: number | null = null;

// 事件監聽器引用
let eventListeners: Array<() => void> = [];

/**
 * 節流版本的 updateUserActivity
 * 確保在指定時間間隔內最多只執行一次更新
 */
function throttledUpdateUserActivity(): void {
  const now = Date.now();
  
  // 如果距離上次更新不足30秒，設置延遲執行
  if (now - lastUpdateTime < THROTTLE_INTERVAL) {
    // 清除之前的延遲執行
    if (throttledUpdateTimeout) {
      clearTimeout(throttledUpdateTimeout);
    }
    
    // 設置新的延遲執行，確保在活動停止後會執行最後一次更新
    const timeToWait = THROTTLE_INTERVAL - (now - lastUpdateTime);
    throttledUpdateTimeout = setTimeout(() => {
      updateUserActivity();
      lastUpdateTime = Date.now();
      throttledUpdateTimeout = null;
    }, timeToWait);
    
    return;
  }
  
  // 直接執行更新
  updateUserActivity();
  lastUpdateTime = now;
}

/**
 * 添加事件監聽器的輔助函數
 */
function addEventListenerWithCleanup(
  target: EventTarget, 
  event: string, 
  handler: EventListener,
  options?: AddEventListenerOptions
): void {
  target.addEventListener(event, handler, options);
  
  // 保存清理函數
  eventListeners.push(() => {
    target.removeEventListener(event, handler, options);
  });
}

/**
 * 啟動全域活動追蹤
 * 監聽各種使用者互動事件
 */
export function startGlobalActivityTracking(): () => void {
  // 只在瀏覽器環境中執行
  if (!browser) {
    return () => {};
  }

  // 清理現有的監聽器
  stopGlobalActivityTracking();

  // 監聽滑鼠事件
  addEventListenerWithCleanup(document, 'click', throttledUpdateUserActivity, { passive: true });
  addEventListenerWithCleanup(document, 'mousedown', throttledUpdateUserActivity, { passive: true });
  addEventListenerWithCleanup(document, 'mousemove', throttledUpdateUserActivity, { passive: true });

  // 監聽鍵盤事件
  addEventListenerWithCleanup(document, 'keydown', throttledUpdateUserActivity, { passive: true });
  addEventListenerWithCleanup(document, 'keyup', throttledUpdateUserActivity, { passive: true });

  // 監聽觸控事件（行動裝置）
  addEventListenerWithCleanup(document, 'touchstart', throttledUpdateUserActivity, { passive: true });
  addEventListenerWithCleanup(document, 'touchmove', throttledUpdateUserActivity, { passive: true });
  addEventListenerWithCleanup(document, 'touchend', throttledUpdateUserActivity, { passive: true });

  // 監聽滾動事件
  addEventListenerWithCleanup(window, 'scroll', throttledUpdateUserActivity, { passive: true });

  // 監聽窗口焦點事件
  addEventListenerWithCleanup(window, 'focus', throttledUpdateUserActivity, { passive: true });

  // 監聽表單互動事件
  addEventListenerWithCleanup(document, 'input', throttledUpdateUserActivity, { passive: true });
  addEventListenerWithCleanup(document, 'change', throttledUpdateUserActivity, { passive: true });

  console.log('全域使用者活動追蹤已啟動');

  // 返回清理函數
  return stopGlobalActivityTracking;
}

/**
 * 停止全域活動追蹤
 * 清理所有事件監聽器
 */
export function stopGlobalActivityTracking(): void {
  // 清理所有事件監聽器
  eventListeners.forEach(cleanup => cleanup());
  eventListeners = [];

  // 清理節流計時器
  if (throttledUpdateTimeout) {
    clearTimeout(throttledUpdateTimeout);
    throttledUpdateTimeout = null;
  }

  console.log('全域使用者活動追蹤已停止');
}

/**
 * 手動觸發活動更新
 * 主要用於重要的業務操作
 */
export function manualUpdateActivity(): void {
  updateUserActivity();
}

/**
 * 獲取節流配置資訊
 */
export function getThrottleConfig(): { interval: number; lastUpdate: number } {
  return {
    interval: THROTTLE_INTERVAL,
    lastUpdate: lastUpdateTime
  };
}