/**
 * 前端應用程式配置
 */

import { browser } from '$app/environment';

// 預設配置
const DEFAULT_CONFIG = {
  API_BASE_URL: 'http://localhost:8080',
  API_TIMEOUT: 10000,
  APP_NAME: 'When Did I Take',
  APP_VERSION: '1.0.0'
};

// 開發環境配置檔案路徑
const DEV_CONFIG_PATH = '/api/config';

// 運行時配置
let runtimeConfig = { ...DEFAULT_CONFIG };

/**
 * 從靜態配置檔案載入配置
 */
async function loadStaticConfig(): Promise<Partial<typeof DEFAULT_CONFIG>> {
  try {
    const response = await fetch('/config.json', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      cache: 'no-cache' // 確保獲取最新配置
    });
    
    if (response.ok) {
      const config = await response.json();
      console.log('已載入靜態配置:', config);
      return config;
    } else {
      console.warn('無法載入靜態配置檔案，使用預設配置');
      return {};
    }
  } catch (error) {
    console.warn('載入靜態配置失敗，使用預設配置:', error);
    return {};
  }
}

/**
 * 初始化配置
 */
async function initConfig(): Promise<void> {
  if (browser) {
    try {
      const staticConfig = await loadStaticConfig();
      runtimeConfig = {
        ...DEFAULT_CONFIG,
        ...staticConfig
      };
      console.log('配置初始化完成:', runtimeConfig);
    } catch (error) {
      console.error('配置初始化失敗，使用預設配置:', error);
    }
  }
}

/**
 * 獲取配置值
 */
export function getConfig<K extends keyof typeof DEFAULT_CONFIG>(
  key: K
): typeof DEFAULT_CONFIG[K] {
  return runtimeConfig[key];
}

/**
 * 獲取API基礎URL
 */
export function getApiBaseUrl(): string {
  return getConfig('API_BASE_URL');
}

/**
 * 更新配置
 */
export function updateConfig(newConfig: Partial<typeof DEFAULT_CONFIG>): void {
  runtimeConfig = {
    ...runtimeConfig,
    ...newConfig
  };
  console.log('配置已更新:', runtimeConfig);
}

/**
 * 重置配置為預設值
 */
export function resetConfig(): void {
  runtimeConfig = { ...DEFAULT_CONFIG };
  console.log('配置已重置為預設值');
}

/**
 * 獲取完整配置
 */
export function getAllConfig(): typeof DEFAULT_CONFIG {
  return { ...runtimeConfig };
}

// 自動初始化配置
if (browser) {
  initConfig().catch(error => {
    console.error('自動配置初始化失敗:', error);
  });
}

// 導出配置初始化函數供手動調用
export { initConfig };