/**
 * 主題相關的工具函數
 * 提供統一的主題處理邏輯，避免重複代碼
 */

export type Theme = 'light' | 'dark' | 'auto';

// 預設主題值
export const DEFAULT_THEME: Theme = 'auto';

// 支援的主題選項
export const THEME_OPTIONS: Theme[] = ['light', 'dark', 'auto'];

/**
 * 從 localStorage 讀取儲存的主題設定
 * 適用於瀏覽器環境和非瀏覽器環境
 */
export function getStoredTheme(): Theme {
  // 檢查是否在瀏覽器環境中
  if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
    return DEFAULT_THEME;
  }
  
  try {
    const stored = localStorage.getItem('theme');
    if (stored && THEME_OPTIONS.includes(stored as Theme)) {
      return stored as Theme;
    }
  } catch (error) {
    // localStorage 不可用或讀取錯誤
  }
  
  return DEFAULT_THEME;
}

/**
 * 檢測系統主題偏好
 * 適用於瀏覽器環境和非瀏覽器環境
 */
export function getSystemTheme(): 'light' | 'dark' {
  // 檢查是否在瀏覽器環境中
  if (typeof window === 'undefined' || !window.matchMedia) {
    return 'light';
  }
  
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
}

/**
 * 計算實際應用的主題
 * 將 'auto' 解析為實際的 light/dark 主題
 */
export function getEffectiveTheme(theme: Theme): 'light' | 'dark' {
  if (theme === 'auto') {
    return getSystemTheme();
  }
  return theme;
}

/**
 * 應用主題到 DOM
 * 更新 document.documentElement 的類別
 */
export function applyTheme(theme: Theme): void {
  // 檢查是否在瀏覽器環境中
  if (typeof document === 'undefined') {
    return;
  }
  
  const effectiveTheme = getEffectiveTheme(theme);
  const root = document.documentElement;
  
  // 移除舊的主題類別
  root.classList.remove('light', 'dark');
  
  // 添加新的主題類別
  root.classList.add(effectiveTheme);
}

/**
 * 儲存主題設定到 localStorage
 */
export function saveTheme(theme: Theme): void {
  // 檢查是否在瀏覽器環境中
  if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
    return;
  }
  
  try {
    localStorage.setItem('theme', theme);
  } catch (error) {
    // localStorage 不可用或寫入錯誤
  }
}

/**
 * 初始化主題（讀取並應用儲存的主題）
 * 適用於頁面載入時的初始化
 */
export function initTheme(): Theme {
  const storedTheme = getStoredTheme();
  applyTheme(storedTheme);
  return storedTheme;
}