/**
 * 日期時間工具函數 - 使用 date-fns 重構
 * 建議安裝: npm install date-fns
 */

// 暫時保留原實現，建議後續遷移到 date-fns
import { 
  format, 
  formatDistanceToNow, 
  isToday, 
  isSameDay, 
  subDays, 
  differenceInMinutes,
  startOfDay,
  parseISO
} from 'date-fns';
import { zhTW } from 'date-fns/locale';

/**
 * 格式化日期為本地時間顯示
 */
export function formatLocalDateTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, 'yyyy/MM/dd HH:mm:ss', { locale: zhTW });
}

/**
 * 格式化時間為簡短格式
 */
export function formatTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, 'HH:mm', { locale: zhTW });
}

/**
 * 檢查是否為今天
 */
export function isDateToday(date: Date | string): boolean {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return isToday(dateObj);
}

/**
 * 檢查兩個日期是否為同一天
 */
export function areSameDay(date1: Date | string, date2: Date | string): boolean {
  const d1 = typeof date1 === 'string' ? parseISO(date1) : date1;
  const d2 = typeof date2 === 'string' ? parseISO(date2) : date2;
  return isSameDay(d1, d2);
}

/**
 * 計算兩個時間的分鐘差
 */
export function getMinutesDifference(date1: Date | string, date2: Date | string): number {
  const d1 = typeof date1 === 'string' ? parseISO(date1) : date1;
  const d2 = typeof date2 === 'string' ? parseISO(date2) : date2;
  return Math.abs(differenceInMinutes(d2, d1));
}

/**
 * 獲取N天前的開始時間
 */
export function getDaysAgoStart(days: number): Date {
  return startOfDay(subDays(new Date(), days));
}

/**
 * 獲取指定天數前開始時間的 ISO 字串（使用 date-fns 實現）
 * @param days 天數
 * @returns 指定天數前開始時間的 ISO 字串
 */
export function getDaysAgoStartISOString(days: number): string {
  const targetDate = startOfDay(subDays(new Date(), days));
  return targetDate.toISOString();
}

/**
 * 格式化相對時間（例如：2小時前）
 */
export function formatRelativeTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return formatDistanceToNow(dateObj, { 
    addSuffix: true, 
    locale: zhTW 
  });
}

/**
 * 獲取當前本地時間的 ISO 字串
 * 確保時區正確性
 */
export function getCurrentLocalISOString(): string {
  const now = new Date();
  // 計算時區偏移量（分鐘）
  const timezoneOffset = now.getTimezoneOffset();
  // 調整為本地時間
  const localTime = new Date(now.getTime() - (timezoneOffset * 60 * 1000));
  // 返回本地時間的 ISO 字串（移除尾部的 Z，因為不是 UTC）
  return localTime.toISOString().slice(0, -1);
}

/**
 * 獲取今天的開始時間（00:00:00）
 */
export function getTodayStartISOString(): string {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  // 計算時區偏移量並調整為本地時間
  const timezoneOffset = now.getTimezoneOffset();
  const localToday = new Date(today.getTime() - (timezoneOffset * 60 * 1000));
  return localToday.toISOString().slice(0, -1);
}


/**
 * 檢查兩個時間是否在同一天（使用 date-fns）
 */
export function isSameDayDate(date1: Date | string, date2: Date | string): boolean {
  const d1 = typeof date1 === 'string' ? parseISO(date1) : date1;
  const d2 = typeof date2 === 'string' ? parseISO(date2) : date2;
  
  return isSameDay(d1, d2);
}
