/**
 * 統一錯誤處理機制
 * 最後更新: 2025/7/22
 */

export enum ErrorType {
  NETWORK_ERROR = 'network_error',
  AUTHENTICATION_ERROR = 'authentication_error',
  VALIDATION_ERROR = 'validation_error',
  PERMISSION_ERROR = 'permission_error',
  NOT_FOUND_ERROR = 'not_found_error',
  SERVER_ERROR = 'server_error',
  UNKNOWN_ERROR = 'unknown_error'
}

export interface AppError {
  type: ErrorType;
  message: string;
  code?: string;
  details?: any;
  timestamp: Date;
  context?: string;
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorListeners: Array<(error: AppError) => void> = [];

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * 處理錯誤並分類
   */
  handleError(error: any, context?: string): AppError {
    const appError = this.classifyError(error, context);
    
    // 記錄錯誤
    console.error(`[${appError.type}] ${appError.message}`, {
      context: appError.context,
      details: appError.details,
      timestamp: appError.timestamp
    });

    // 通知監聽器
    this.notifyListeners(appError);

    return appError;
  }

  /**
   * 分類錯誤類型
   */
  private classifyError(error: any, context?: string): AppError {
    const timestamp = new Date();

    // 網路錯誤
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return {
        type: ErrorType.NETWORK_ERROR,
        message: '網路連線失敗，請檢查網路狀態',
        timestamp,
        context,
        details: error
      };
    }

    // HTTP 錯誤
    if (error.status) {
      switch (error.status) {
        case 401:
          return {
            type: ErrorType.AUTHENTICATION_ERROR,
            message: '認證失效，請重新登入',
            code: '401',
            timestamp,
            context,
            details: error
          };
        case 403:
          return {
            type: ErrorType.PERMISSION_ERROR,
            message: '權限不足，無法執行此操作',
            code: '403',
            timestamp,
            context,
            details: error
          };
        case 404:
          return {
            type: ErrorType.NOT_FOUND_ERROR,
            message: '請求的資源不存在',
            code: '404',
            timestamp,
            context,
            details: error
          };
        case 422:
          return {
            type: ErrorType.VALIDATION_ERROR,
            message: '資料驗證失敗，請檢查輸入內容',
            code: '422',
            timestamp,
            context,
            details: error
          };
        case 500:
        case 502:
        case 503:
          return {
            type: ErrorType.SERVER_ERROR,
            message: '伺服器發生錯誤，請稍後再試',
            code: error.status.toString(),
            timestamp,
            context,
            details: error
          };
      }
    }

    // GraphQL 錯誤
    if (error.graphQLErrors && error.graphQLErrors.length > 0) {
      const gqlError = error.graphQLErrors[0];
      return {
        type: ErrorType.VALIDATION_ERROR,
        message: gqlError.message || 'GraphQL 查詢失敗',
        timestamp,
        context,
        details: error
      };
    }

    // 預設錯誤
    return {
      type: ErrorType.UNKNOWN_ERROR,
      message: error.message || '發生未知錯誤',
      timestamp,
      context,
      details: error
    };
  }

  /**
   * 添加錯誤監聽器
   */
  addErrorListener(listener: (error: AppError) => void): () => void {
    this.errorListeners.push(listener);
    
    // 返回移除監聽器的函數
    return () => {
      const index = this.errorListeners.indexOf(listener);
      if (index > -1) {
        this.errorListeners.splice(index, 1);
      }
    };
  }

  /**
   * 通知所有監聽器
   */
  private notifyListeners(error: AppError): void {
    this.errorListeners.forEach(listener => {
      try {
        listener(error);
      } catch (e) {
        console.error('錯誤監聽器執行失敗:', e);
      }
    });
  }

  /**
   * 獲取使用者友善的錯誤訊息
   */
  getUserFriendlyMessage(error: AppError): string {
    switch (error.type) {
      case ErrorType.NETWORK_ERROR:
        return '網路連線有問題，請檢查網路設定後重試';
      case ErrorType.AUTHENTICATION_ERROR:
        return '登入已過期，請重新登入';
      case ErrorType.PERMISSION_ERROR:
        return '您沒有權限執行此操作';
      case ErrorType.VALIDATION_ERROR:
        return '輸入的資料有誤，請檢查後重試';
      case ErrorType.NOT_FOUND_ERROR:
        return '找不到請求的資料';
      case ErrorType.SERVER_ERROR:
        return '伺服器暫時無法處理請求，請稍後再試';
      default:
        return '發生未預期的錯誤，請聯繫技術支援';
    }
  }
}

// 全域錯誤處理器實例
export const errorHandler = ErrorHandler.getInstance();

// 便利函數
export function handleError(error: any, context?: string): AppError {
  return errorHandler.handleError(error, context);
}

export function addErrorListener(listener: (error: AppError) => void): () => void {
  return errorHandler.addErrorListener(listener);
}
