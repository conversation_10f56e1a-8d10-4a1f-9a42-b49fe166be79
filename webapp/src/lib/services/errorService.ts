/**
 * 統一錯誤處理服務
 * 最後更新: 2025/7/21
 */

import { goto } from '$app/navigation';
import { clearToken } from '$lib/auth';

export enum ErrorType {
  /** 網路連線錯誤 */
  NETWORK = 'network',
  /** 認證錯誤 */
  AUTH = 'auth',
  /** 業務邏輯錯誤 */
  BUSINESS = 'business',
  /** 資料驗證錯誤 */
  VALIDATION = 'validation',
  /** 請求超時錯誤 */
  TIMEOUT = 'timeout',
  /** 未知錯誤 */
  UNKNOWN = 'unknown'
}

export interface ErrorInfo {
  type: ErrorType;
  message: string;
  context?: string;
  originalError?: Error;
  timestamp: Date;
  canRetry: boolean;
}

export class ErrorService {
  private static instance: ErrorService;
  private errorListeners: ((error: ErrorInfo) => void)[] = [];

  static getInstance(): ErrorService {
    if (!ErrorService.instance) {
      ErrorService.instance = new ErrorService();
    }
    return ErrorService.instance;
  }

  /**
   * 處理錯誤
   */
  handle(error: unknown, context?: string): ErrorInfo {
    const errorInfo = this.parseError(error, context);
    
    // 記錄錯誤
    console.error(`[${errorInfo.type}] ${errorInfo.message}`, {
      context: errorInfo.context,
      timestamp: errorInfo.timestamp,
      originalError: errorInfo.originalError
    });

    // 特殊錯誤處理
    switch (errorInfo.type) {
      case ErrorType.AUTH:
        this.handleAuthError();
        break;
      case ErrorType.NETWORK:
        // 網路錯誤可能需要特殊處理
        break;
    }

    // 通知錯誤監聽器
    this.notifyListeners(errorInfo);

    return errorInfo;
  }

  /**
   * 解析錯誤類型和訊息
   */
  private parseError(error: unknown, context?: string): ErrorInfo {
    let type = ErrorType.UNKNOWN;
    let message = '未知錯誤';
    let canRetry = true;
    let originalError: Error | undefined;

    if (error instanceof Error) {
      originalError = error;
      message = error.message;

      // 根據錯誤訊息判斷類型
      if (message.includes('認證') || message.includes('權杖') || message.includes('登入')) {
        type = ErrorType.AUTH;
        canRetry = false;
      } else if (message.includes('網路') || message.includes('連線') || error.name === 'NetworkError') {
        type = ErrorType.NETWORK;
      } else if (message.includes('timeout') || message.includes('超時')) {
        type = ErrorType.TIMEOUT;
      } else if (message.includes('驗證') || message.includes('格式')) {
        type = ErrorType.VALIDATION;
        canRetry = false;
      } else {
        type = ErrorType.BUSINESS;
      }
    } else if (typeof error === 'string') {
      message = error;
    }

    return {
      type,
      message,
      context,
      originalError,
      timestamp: new Date(),
      canRetry
    };
  }

  /**
   * 處理認證錯誤
   */
  private handleAuthError(): void {
    // 清除認證狀態
    clearToken();
    
    // 跳轉到登入頁面
    goto('/login');
    
    // 可以在這裡顯示通知
    console.warn('認證失效，已自動登出');
  }

  /**
   * 重試機制
   */
  async retry<T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    delayMs: number = 1000,
    backoffMultiplier: number = 2
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // 如果是最後一次嘗試，拋出錯誤
        if (attempt === maxAttempts) {
          throw lastError;
        }

        // 檢查是否可以重試
        const errorInfo = this.parseError(lastError);
        if (!errorInfo.canRetry) {
          throw lastError;
        }

        // 等待後重試（指數退避）
        const delay = delayMs * Math.pow(backoffMultiplier, attempt - 1);
        await this.sleep(delay);
        
        console.warn(`操作失敗，正在重試 (${attempt}/${maxAttempts})`, lastError.message);
      }
    }

    throw lastError!;
  }

  /**
   * 添加錯誤監聽器
   */
  addErrorListener(listener: (error: ErrorInfo) => void): void {
    this.errorListeners.push(listener);
  }

  /**
   * 移除錯誤監聽器
   */
  removeErrorListener(listener: (error: ErrorInfo) => void): void {
    const index = this.errorListeners.indexOf(listener);
    if (index > -1) {
      this.errorListeners.splice(index, 1);
    }
  }

  /**
   * 通知所有錯誤監聽器
   */
  private notifyListeners(error: ErrorInfo): void {
    this.errorListeners.forEach(listener => {
      try {
        listener(error);
      } catch (err) {
        console.error('錯誤監聽器執行失敗:', err);
      }
    });
  }

  /**
   * 延遲函數
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 創建帶 timeout 的 fetch
   */
  async fetchWithTimeout(url: string, options: RequestInit = {}, timeoutMs: number = 30000): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`請求超時 (${timeoutMs}ms)`);
      }
      throw error;
    }
  }
}

// 匯出單例實例
export const errorService = ErrorService.getInstance();