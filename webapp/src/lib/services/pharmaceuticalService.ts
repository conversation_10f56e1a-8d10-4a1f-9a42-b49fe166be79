/**
 * 藥物服務 - 統一的藥物和服藥記錄管理
 * 替代 pharmaceuticalStore 的功能
 * 最後更新: 2025/7/22
 */

import { get } from 'svelte/store';
import { dataStore, dataStoreActions, generateQuickPharmaceuticalOptions } from '$lib/stores/dataStore';
import { queryTakes, queryPharmaceuticals } from '$lib/graphql/services';
import { LoadingState } from '$lib/types/loadingTypes';
import type { 
  PharmaceuticalTakeRecord, 
  AvailablePharmaceutical, 
  QuickPharmaceuticalOption 
} from '$lib/types/pharmaceutical';

/**
 * 服藥記錄管理類
 */
export class PharmaceuticalService {
  private static instance: PharmaceuticalService;

  static getInstance(): PharmaceuticalService {
    if (!PharmaceuticalService.instance) {
      PharmaceuticalService.instance = new PharmaceuticalService();
    }
    return PharmaceuticalService.instance;
  }

  /**
   * 載入藥物清單
   */
  async loadAvailablePharmaceuticals(): Promise<AvailablePharmaceutical[]> {
    if (dataStoreActions.isLoading('pharmaceuticals')) {
      return get(dataStore).pharmaceuticals.data || [];
    }

    dataStoreActions.setPharmaceuticalsLoading(LoadingState.LOADING);
    
    try {
      const pharmaceuticals = await queryPharmaceuticals();
      
      // 轉換格式以符合 AvailablePharmaceutical 介面
      const availablePharmaceuticals: AvailablePharmaceutical[] = pharmaceuticals.map(p => ({
        id: p.id,
        name: p.name,
        manufacturer: p.manufacturer,
        activeIngredient: p.activeIngredient,
        unit: p.dosageUnit,
        dosagePerServing: p.dosagePerServing,
        servingUnitName: p.servingUnitName,
        currentPlan: p.currentPlan,
        description: p.description
      }));

      dataStoreActions.setPharmaceuticalsData(availablePharmaceuticals);
      return availablePharmaceuticals;
    } catch (error) {
      console.error('載入藥物清單失敗:', error);
      dataStoreActions.setPharmaceuticalsLoading(LoadingState.ERROR, String(error));
      return [];
    }
  }

  /**
   * 載入服藥記錄
   */
  async loadPharmaceuticalTakeRecords(): Promise<PharmaceuticalTakeRecord[]> {
    if (dataStoreActions.isLoading('takeRecords')) {
      return get(dataStore).takeRecords.data || [];
    }

    dataStoreActions.setTakeRecordsLoading(LoadingState.LOADING);
    
    try {
      const takes = await queryTakes({ limit: 100, offset: 0 });
      
      // 轉換格式以符合 PharmaceuticalTakeRecord 介面
      const takeRecords: PharmaceuticalTakeRecord[] = takes.map(take => ({
        id: take.id,
        pharmaceuticalId: take.pharmaceuticalId,
        pharmaceuticalName: take.pharmaceutical?.name || '未知藥物',
        dosage: take.quantity,
        dosageUnit: take.pharmaceutical?.dosageUnit || '',
        dosagePerServing: take.pharmaceutical?.dosagePerServing || 1,
        servingUnitName: take.pharmaceutical?.servingUnitName,
        takenAt: take.takenAt,
        notes: take.notes,
        createdBy: take.userId
      }));

      dataStoreActions.setTakeRecordsData(takeRecords);
      return takeRecords;
    } catch (error) {
      console.error('載入服藥記錄失敗:', error);
      dataStoreActions.setTakeRecordsLoading(LoadingState.ERROR, String(error));
      return [];
    }
  }

  /**
   * 生成快速選項
   */
  async generateQuickOptions(): Promise<QuickPharmaceuticalOption[]> {
    const store = get(dataStore);
    
    // 確保資料已載入
    const takeRecords = store.takeRecords.data || await this.loadPharmaceuticalTakeRecords();
    const pharmaceuticals = store.pharmaceuticals.data || await this.loadAvailablePharmaceuticals();

    if (!takeRecords.length || !pharmaceuticals.length) {
      dataStoreActions.setQuickOptionsData([]);
      return [];
    }

    dataStoreActions.setQuickOptionsLoading(LoadingState.LOADING);

    try {
      const quickOptions = generateQuickPharmaceuticalOptions(takeRecords, pharmaceuticals);
      dataStoreActions.setQuickOptionsData(quickOptions);
      return quickOptions;
    } catch (error) {
      console.error('生成快速選項失敗:', error);
      dataStoreActions.setQuickOptionsLoading(LoadingState.ERROR, String(error));
      return [];
    }
  }

  /**
   * 刷新所有資料
   */
  async refreshAllData(): Promise<void> {
    await Promise.all([
      this.loadAvailablePharmaceuticals(),
      this.loadPharmaceuticalTakeRecords()
    ]);
    
    await this.generateQuickOptions();
  }

  /**
   * 當服藥記錄變更時的回調
   */
  async onPharmaceuticalTakeRecordChanged(): Promise<void> {
    // 重新載入服藥記錄
    await this.loadPharmaceuticalTakeRecords();
    
    // 重新生成快速選項
    await this.generateQuickOptions();
  }

  /**
   * 取得格式化顯示函數（保持與舊版相容）
   */
  getServingUnitDisplay(servingUnitName?: string | null): string {
    return servingUnitName || '份';
  }

  formatDosageDisplay(
    absoluteDosage: number, 
    dosagePerServing: number, 
    dosageUnit: string, 
    servingUnitName?: string | null
  ): string {
    const servings = absoluteDosage / dosagePerServing;
    const unitDisplay = this.getServingUnitDisplay(servingUnitName);
    
    if (servings === 1) {
      return `${absoluteDosage} ${dosageUnit} (1 ${unitDisplay})`;
    } else {
      return `${absoluteDosage} ${dosageUnit} (${servings} ${unitDisplay})`;
    }
  }

  formatDailyTotalDisplay(
    count: number, 
    totalServings: number, 
    totalDosage: number, 
    dosageUnit: string, 
    servingUnitName?: string | null
  ): string {
    const unitDisplay = this.getServingUnitDisplay(servingUnitName);
    
    if (count === 1) {
      return `今日 1 次，${totalDosage} ${dosageUnit} (${totalServings} ${unitDisplay})`;
    } else {
      return `今日 ${count} 次，${totalDosage} ${dosageUnit} (${totalServings} ${unitDisplay})`;
    }
  }
}

// 匯出單例實例
export const pharmaceuticalService = PharmaceuticalService.getInstance();