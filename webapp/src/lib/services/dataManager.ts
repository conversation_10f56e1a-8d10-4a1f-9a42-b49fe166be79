/**
 * 統一資料管理器 - 實作單一資料源原則
 * 最後更新: 2025/7/21
 */

import type { UserInfo } from '$lib/auth';
import type { PharmaceuticalTakeRecord, AvailablePharmaceutical } from '$lib/types/pharmaceutical';
import { LoadingState } from '$lib/types/loadingTypes';
import { dataStoreActions } from '$lib/stores/dataStore';
import { errorService } from './errorService';
import { get } from 'svelte/store';
import { dataStore } from '$lib/stores/dataStore';
import { queryTakes, queryPharmaceuticals, queryUser } from '$lib/graphql/services';

export class DataManager {
  private static instance: DataManager;
  private loadingPromises = new Map<string, Promise<any>>();

  static getInstance(): DataManager {
    if (!DataManager.instance) {
      DataManager.instance = new DataManager();
    }
    return DataManager.instance;
  }

  /**
   * 載入使用者資訊
   */
  async loadUserInfo(forceReload = false): Promise<UserInfo | null> {
    const cacheKey = 'userInfo';
    
    // 檢查是否正在載入
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    // 檢查是否需要載入
    if (!forceReload && dataStoreActions.hasData('userInfo') && !dataStoreActions.needsRefresh('userInfo')) {
      const store = get(dataStore);
      return store.userInfo.data;
    }

    const loadPromise = this.executeLoad(
      cacheKey,
      () => this.fetchUserInfo(),
      (data) => dataStoreActions.setUserInfoData(data),
      () => dataStoreActions.setUserInfoLoading(LoadingState.LOADING),
      (error) => dataStoreActions.setUserInfoLoading(LoadingState.ERROR, error)
    );

    return loadPromise;
  }

  /**
   * 載入藥物清單
   */
  async loadPharmaceuticals(forceReload = false): Promise<AvailablePharmaceutical[] | null> {
    const cacheKey = 'pharmaceuticals';
    
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    if (!forceReload && dataStoreActions.hasData('pharmaceuticals') && !dataStoreActions.needsRefresh('pharmaceuticals')) {
      const store = get(dataStore);
      return store.pharmaceuticals.data;
    }

    const loadPromise = this.executeLoad(
      cacheKey,
      () => this.fetchPharmaceuticals(),
      (data) => {
        dataStoreActions.setPharmaceuticalsData(data);
        // 同時更新舊的 store 以保持兼容性
        this.updateLegacyPharmaceuticalStore(data);
      },
      () => dataStoreActions.setPharmaceuticalsLoading(LoadingState.LOADING),
      (error) => dataStoreActions.setPharmaceuticalsLoading(LoadingState.ERROR, error)
    );

    return loadPromise;
  }

  /**
   * 載入服藥記錄
   */
  async loadTakeRecords(forceReload = false): Promise<PharmaceuticalTakeRecord[] | null> {
    const cacheKey = 'takeRecords';
    
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    if (!forceReload && dataStoreActions.hasData('takeRecords') && !dataStoreActions.needsRefresh('takeRecords')) {
      const store = get(dataStore);
      return store.takeRecords.data;
    }

    const loadPromise = this.executeLoad(
      cacheKey,
      () => this.fetchTakeRecords(),
      (data) => {
        dataStoreActions.setTakeRecordsData(data);
        // 同時更新舊的 store 以保持兼容性
        this.updateLegacyTakeRecordsStore(data);
      },
      () => dataStoreActions.setTakeRecordsLoading(LoadingState.LOADING),
      (error) => dataStoreActions.setTakeRecordsLoading(LoadingState.ERROR, error)
    );

    return loadPromise;
  }

  /**
   * 執行載入操作的通用方法
   */
  private async executeLoad<T>(
    cacheKey: string,
    fetcher: () => Promise<T>,
    onSuccess: (data: T) => void,
    onStart: () => void,
    onError: (error: string) => void
  ): Promise<T | null> {
    onStart();

    const loadPromise = errorService.retry(async () => {
      try {
        const data = await fetcher();
        onSuccess(data);
        this.loadingPromises.delete(cacheKey);
        return data;
      } catch (error) {
        this.loadingPromises.delete(cacheKey);
        const errorInfo = errorService.handle(error, `載入${cacheKey}失敗`);
        onError(errorInfo.message);
        throw error;
      }
    }, 3, 1000, 2);

    this.loadingPromises.set(cacheKey, loadPromise);

    try {
      return await loadPromise;
    } catch (error) {
      return null;
    }
  }

  /**
   * 清除所有載入中的 Promise
   */
  clearLoadingPromises(): void {
    this.loadingPromises.clear();
  }

  /**
   * 清除所有資料
   */
  clearAllData(): void {
    this.clearLoadingPromises();
    dataStoreActions.clearAllData();
    // 清理舊的 store
    this.clearLegacyStores();
  }

  /**
   * 更新舊的藥物 store（兼容性）
   */
  private updateLegacyPharmaceuticalStore(data: AvailablePharmaceutical[]): void {
    try {
      import('$lib/pharmaceuticalStore').then(({ availablePharmaceuticals }) => {
        // 轉換為舊格式
        const legacyData = data.map(item => ({
          id: item.id,
          name: item.name,
          dosageUnit: item.unit,
          dosagePerServing: item.dosagePerServing || 0,
          servingUnitName: item.servingUnitName || item.unit,
          currentPlan: item.currentPlan || false,
          description: item.description
        }));
        availablePharmaceuticals.set(legacyData);
      });
    } catch (error) {
      console.warn('更新舊藥物 store 失敗:', error);
    }
  }

  /**
   * 更新舊的服藥記錄 store（兼容性）
   */
  private updateLegacyTakeRecordsStore(data: PharmaceuticalTakeRecord[]): void {
    try {
      import('$lib/pharmaceuticalStore').then(({ pharmaceuticalTakeRecords }) => {
        console.log('=== DataManager 更新舊格式服藥記錄 ===');
        console.log('新格式記錄數量:', data.length);
        console.log('新格式記錄樣本:', data.slice(0, 2));
        
        // 轉換為舊格式
        const legacyData = data.map(record => ({
          id: record.id,
          pharmaceuticalId: record.pharmaceutical.id,
          pharmaceuticalName: record.pharmaceutical.name,
          dosage: record.quantity,
          dosageUnit: record.pharmaceutical.unit,
          dosagePerServing: record.quantity,
          servingUnitName: record.pharmaceutical.unit,
          takenAt: record.takenAt,
          notes: record.note || '',
          createdBy: record.pharmaceutical.createdBy || 1 // 使用從 GraphQL 查詢取得的建立者ID，預設為1
        }));
        
        console.log('轉換後的舊格式記錄樣本:', legacyData.slice(0, 2));
        pharmaceuticalTakeRecords.set(legacyData);
        
        // 重新生成快速選項
        import('$lib/pharmaceuticalStore').then(async ({ 
          generateQuickPharmaceuticalOptionsSync,
          quickPharmaceuticalOptions,
          availablePharmaceuticals
        }) => {
          // 動態匯入 get 函數
          const { get: storeGet } = await import('svelte/store');
          
          // 確保藥物清單已載入
          const pharmaceuticals = storeGet(availablePharmaceuticals);
          console.log('當前藥物清單:', pharmaceuticals);
          
          const quickOptions = generateQuickPharmaceuticalOptionsSync(legacyData);
          console.log('重新生成的快速選項:', quickOptions);
          quickPharmaceuticalOptions.set(quickOptions);
        });
        
        console.log('=== DataManager 更新完成 ===');
      });
    } catch (error) {
      console.warn('更新舊服藥記錄 store 失敗:', error);
    }
  }

  /**
   * 清理舊的 store
   */
  private clearLegacyStores(): void {
    try {
      import('$lib/pharmaceuticalStore').then(({ 
        availablePharmaceuticals, 
        pharmaceuticalTakeRecords,
        quickPharmaceuticalOptions
      }) => {
        availablePharmaceuticals.set([]);
        pharmaceuticalTakeRecords.set([]);
        quickPharmaceuticalOptions.set([]);
      });
    } catch (error) {
      console.warn('清理舊 store 失敗:', error);
    }
  }

  // 私有方法：實際的 API 呼叫

  private async fetchUserInfo(): Promise<UserInfo> {
    try {
      const user = await queryUser();
      return {
        id: user.id,
        name: user.name,
        email: user.email
      };
    } catch (error) {
      throw new Error(`無法取得使用者資訊: ${error}`);
    }
  }

  private async fetchPharmaceuticals(): Promise<AvailablePharmaceutical[]> {
    try {
      const pharmaceuticals = await queryPharmaceuticals();
      
      // 轉換為 AvailablePharmaceutical 格式
      return pharmaceuticals.map((pharm) => ({
        id: pharm.id,
        name: pharm.name,
        unit: pharm.dosageUnit,
        dosagePerServing: pharm.dosagePerServing || 0,
        servingUnitName: pharm.servingUnitName,
        currentPlan: pharm.currentPlan || false,
        description: pharm.description
      }));
    } catch (error) {
      throw new Error(`載入藥物清單失敗: ${error}`);
    }
  }

  private async fetchTakeRecords(): Promise<PharmaceuticalTakeRecord[]> {
    try {
      const takes = await queryTakes({ limit: 50 });
      
      // 使用 GraphQL 返回的完整藥物資訊
      return takes.map((take) => ({
        id: take.id,
        pharmaceutical: {
          id: take.pharmaceutical!.id,
          name: take.pharmaceutical!.name,
          unit: take.pharmaceutical!.dosageUnit,
          description: take.pharmaceutical!.description || ''
        },
        quantity: take.quantity,
        takenAt: take.takenAt,
        createdAt: take.takenAt,
        note: take.notes || ''
      }));
    } catch (error) {
      throw new Error(`載入服藥記錄失敗: ${error}`);
    }
  }
}

// 匯出單例實例
export const dataManager = DataManager.getInstance();