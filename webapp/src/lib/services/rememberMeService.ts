/**
 * 記住我功能服務
 * 最後更新: 2025/7/21
 */

interface RememberedCredentials {
  userName: string;
  rememberMe: boolean;
}

export class RememberMeService {
  private static instance: RememberMeService;
  private storageKey = 'rememberedCredentials';

  static getInstance(): RememberMeService {
    if (!RememberMeService.instance) {
      RememberMeService.instance = new RememberMeService();
    }
    return RememberMeService.instance;
  }

  /**
   * 儲存記住的登入資訊
   */
  save(credentials: RememberedCredentials): void {
    try {
      if (credentials.rememberMe && typeof window !== 'undefined') {
        localStorage.setItem(this.storageKey, JSON.stringify(credentials));
      } else {
        this.clear();
      }
    } catch (error) {
      console.warn('儲存記住的登入資訊失敗:', error);
    }
  }

  /**
   * 載入記住的登入資訊
   */
  load(): RememberedCredentials | null {
    try {
      if (typeof window === 'undefined') {
        return null;
      }

      const stored = localStorage.getItem(this.storageKey);
      if (!stored) {
        return null;
      }

      const credentials = JSON.parse(stored) as RememberedCredentials;
      
      // 驗證資料格式
      if (typeof credentials.userName === 'string' && typeof credentials.rememberMe === 'boolean') {
        return credentials;
      }

      // 格式不正確，清除資料
      this.clear();
      return null;
    } catch (error) {
      console.warn('載入記住的登入資訊失敗:', error);
      this.clear();
      return null;
    }
  }

  /**
   * 清除記住的登入資訊
   */
  clear(): void {
    try {
      if (typeof window !== 'undefined') {
        localStorage.removeItem(this.storageKey);
      }
    } catch (error) {
      console.warn('清除記住的登入資訊失敗:', error);
    }
  }

  /**
   * 檢查是否有記住的登入資訊
   */
  hasRemembered(): boolean {
    const credentials = this.load();
    return credentials !== null && credentials.rememberMe;
  }
}

// 匯出單例實例
export const rememberMeService = RememberMeService.getInstance();