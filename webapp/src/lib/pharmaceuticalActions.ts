import { createTake as urqlCreateTake, deleteTake as urqlDeleteTake, updateTake as urqlUpdateTake } from './graphql/services';
import { onPharmaceuticalTakeRecordChanged } from './pharmaceuticalStore';
import { getCurrentLocalISOString, getMinutesDifference } from './utils/dateHelpers';
import { getDaysAgoStartISOString } from './utils/dateHelpers';
import type { AddPharmaceuticalTakeInput, UpdatePharmaceuticalTakeInput } from './types/pharmaceutical';

export async function addPharmaceuticalTake(input: AddPharmaceuticalTakeInput): Promise<any> {
  try {
    // 驗證輸入
    if (!input.userId || !input.pharmaceuticalId || input.quantity <= 0) {
      throw new Error('用戶ID、藥物ID和劑量為必填項目');
    }

    const takeInput = {
      userId: input.userId,
      pharmaceuticalId: input.pharmaceuticalId,
      quantity: input.quantity,
      takenAt: input.takenAt || getCurrentLocalISOString(),
      notes: input.notes || ''
    };

    const result = await urqlCreateTake({ take: takeInput });

    // 檢查是否需要更新本地資料
    const takenDate = new Date(takeInput.takenAt);
    const thirtyDaysAgoISO = getDaysAgoStartISOString(30);
    const thirtyDaysAgo = new Date(thirtyDaysAgoISO);

    // 立即追蹤新增的記錄，防止競態條件
    recentlyAddedTakes.set(takeInput.pharmaceuticalId, new Date(takeInput.takenAt));

    if (takenDate >= thirtyDaysAgo) {
      await onPharmaceuticalTakeRecordChanged();
    }

    return result;
  } catch (error) {
    console.error('新增服藥紀錄失敗:', error);
    throw error;
  }
}


// 新增：更新服藥紀錄
export async function updatePharmaceuticalTake(
  input: UpdatePharmaceuticalTakeInput
): Promise<any> {
  try {
    if (!input.id) {
      throw new Error('ID為必填項目');
    }

    const takeInput = {
      id: input.id,
      quantity: input.quantity,
      takenAt: input.takenAt,
      notes: input.notes
    };

    const result = await urqlUpdateTake({ take: takeInput });

    await onPharmaceuticalTakeRecordChanged();
    return result;
  } catch (error) {
    console.error('更新服藥紀錄失敗:', error);
    throw error;
  }
}

// 修正刪除服藥紀錄
export async function deletePharmaceuticalTake(id: number): Promise<boolean> {
  try {
    if (!id) {
      throw new Error('ID為必填項目');
    }

    const success = await urqlDeleteTake({ takeId: id });

    if (success) {
      await onPharmaceuticalTakeRecordChanged();
    }

    return success;
  } catch (error) {
    console.error('刪除服藥紀錄失敗:', error);
    throw error;
  }
}

// 全域追蹤最近新增的記錄，防止競態條件
const recentlyAddedTakes = new Map<number, Date>();

// 檢查30分鐘內是否服用過相同藥物
export async function checkRecentTake(pharmaceuticalId: number): Promise<{
  hasRecentTake: boolean;
  lastTakeTime?: string;
  minutesAgo?: number;
}> {
  try {
    const { get } = await import('svelte/store');
    const { pharmaceuticalTakeRecords } = await import('./pharmaceuticalStore');

    const records = get(pharmaceuticalTakeRecords);
    const now = new Date();

    // 檢查全域追蹤的最近新增記錄
    const recentlyAdded = recentlyAddedTakes.get(pharmaceuticalId);
    if (recentlyAdded) {
      const minutesAgo = getMinutesDifference(recentlyAdded, now);
      if (minutesAgo <= 30) {
        return {
          hasRecentTake: true,
          lastTakeTime: recentlyAdded.toISOString(),
          minutesAgo: Math.round(minutesAgo)
        };
      } else {
        // 超過30分鐘，清除追蹤
        recentlyAddedTakes.delete(pharmaceuticalId);
      }
    }

    // 過濾出相同藥物的紀錄
    const samePharmaceuticalRecords = records.filter(record =>
      record.pharmaceuticalId === pharmaceuticalId
    );

    // 檢查30分鐘內的紀錄
    for (const record of samePharmaceuticalRecords) {
      const recordTime = new Date(record.takenAt);
      const minutesAgo = getMinutesDifference(recordTime, now);

      if (minutesAgo <= 30) {
        return {
          hasRecentTake: true,
          lastTakeTime: record.takenAt,
          minutesAgo: Math.round(minutesAgo)
        };
      }
    }

    return { hasRecentTake: false };
  } catch (error: unknown) {
    console.error('檢查近期服藥紀錄失敗:', error);
    return { hasRecentTake: false };
  }
}

// 新增：驗證藥物是否存在
export async function validatePharmaceuticalExists(pharmaceuticalId: number): Promise<boolean> {
  try {
    const { availablePharmaceuticals } = await import('./pharmaceuticalStore');
    const { get } = await import('svelte/store');
    const pharmaceuticals = get(availablePharmaceuticals);

    if (pharmaceuticals.length === 0) {
      const { loadAvailablePharmaceuticals } = await import('./pharmaceuticalStore');
      await loadAvailablePharmaceuticals();
      const updatedPharmaceuticals = get(availablePharmaceuticals);
      return updatedPharmaceuticals.some(p => p.id === pharmaceuticalId);
    }

    return pharmaceuticals.some(p => p.id === pharmaceuticalId);
  } catch (error) {
    console.error('驗證藥物失敗:', error);
    return false;
  }
}

// 修正快速新增服藥
export async function quickAddPharmaceutical(
  pharmaceuticalId: number,
  quantity: number,
  notes?: string
): Promise<any> {
  // 先驗證藥物是否存在
  const exists = await validatePharmaceuticalExists(pharmaceuticalId);
  if (!exists) {
    throw new Error('指定的藥物不存在');
  }

  return addPharmaceuticalTake({
    userId: 1, // TODO: 從認證狀態取得實際 userId
    pharmaceuticalId,
    quantity,
    takenAt: getCurrentLocalISOString(),
    notes
  });
}

// 批次新增服藥紀錄
export async function batchAddPharmaceuticalTakes(
  inputs: AddPharmaceuticalTakeInput[]
): Promise<any[]> {
  const results = [];

  for (const input of inputs) {
    try {
      const result = await addPharmaceuticalTake(input);
      results.push(result);
    } catch (error) {
      console.error('批次新增服藥紀錄失敗:', error);
      results.push({ error: error.message });
    }
  }

  return results;
}
