/**
 * 主題狀態管理
 * 支援 light/dark 模式切換並同步到 localStorage
 */

import { writable } from 'svelte/store';
import { browser } from '$app/environment';
import { 
  type Theme, 
  getStoredTheme, 
  getSystemTheme, 
  getEffectiveTheme, 
  applyTheme, 
  saveTheme 
} from './utils/themeUtils';

// 創建主題 store
function createThemeStore() {
  const { subscribe, set, update } = writable<Theme>(getStoredTheme());

  return {
    subscribe,
    
    // 設定主題
    setTheme: (newTheme: Theme) => {
      if (!browser) return;
      
      try {
        saveTheme(newTheme);
        set(newTheme);
        applyThemeWithTransition(newTheme);
      } catch (error) {
        console.error('無法儲存主題設定:', error);
      }
    },
    
    // 切換 light/dark 模式
    toggle: () => {
      update(currentTheme => {
        const effectiveTheme = getEffectiveTheme(currentTheme);
        const newTheme = effectiveTheme === 'dark' ? 'light' : 'dark';
        
        try {
          saveTheme(newTheme);
          applyThemeWithTransition(newTheme);
        } catch (error) {
          console.error('無法儲存主題設定:', error);
        }
        
        return newTheme;
      });
    },
    
    // 初始化主題
    init: () => {
      if (!browser) return;
      
      const storedTheme = getStoredTheme();
      set(storedTheme);
      applyThemeWithTransition(storedTheme);
      
      // 監聽系統主題變化
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleSystemThemeChange = () => {
        update(currentTheme => {
          if (currentTheme === 'auto') {
            applyThemeWithTransition('auto');
          }
          return currentTheme;
        });
      };
      
      mediaQuery.addEventListener('change', handleSystemThemeChange);
      
      // 返回清理函數
      return () => {
        mediaQuery.removeEventListener('change', handleSystemThemeChange);
      };
    }
  };
}

// 擴展 applyTheme 以支援轉場效果
function applyThemeWithTransition(theme: Theme) {
  if (!browser) return;
  
  applyTheme(theme);
  
  // 為了無縫切換，使用 transition
  const root = document.documentElement;
  if (!root.style.transition) {
    root.style.transition = 'background-color 0.3s ease, color 0.3s ease';
  }
}

// 導出主題 store
export const theme = createThemeStore();

// 導出工具函數
export { getEffectiveTheme, getSystemTheme } from './utils/themeUtils';