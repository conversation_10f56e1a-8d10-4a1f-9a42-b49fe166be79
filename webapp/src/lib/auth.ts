import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';
import { goto } from '$app/navigation';
import Cookies from 'js-cookie';
import { z } from 'zod';
import { getApiBaseUrl } from './config';
import { dataManager } from './services/dataManager';

// 型別定義
const LoginRequestSchema = z.object({
  user_id: z.number(),
  password: z.string()
});

const LoginResponseSchema = z.object({
  access_token: z.string(),
  token_type: z.string(),
  expires_in: z.number()
});

const UserInfoSchema = z.object({
  id: z.number(),
  name: z.string(),
  email: z.string()
});

export type LoginRequest = z.infer<typeof LoginRequestSchema>;
export type LoginResponse = z.infer<typeof LoginResponseSchema>;
export type UserInfo = z.infer<typeof UserInfoSchema>;

// Store 定義
export const authToken = writable<string | null>(null);
export const currentUser = writable<UserInfo | null>(null);
export const isAuthenticated = derived(authToken, ($token) => $token !== null);
export const lastUserActivity = writable<Date | null>(null);
export const lastDataFetch = writable<Date | null>(null);

// JWT 相關工具函數
export function parseJWT(token: string): any {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(c =>
      '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
    ).join(''));

    return JSON.parse(jsonPayload);
  } catch (e) {
    return null;
  }
}

// 更新使用者操作時間
export function updateUserActivity() {
  lastUserActivity.set(new Date());
}

// 檢查JWT是否需要更新
export function checkTokenRefreshNeeded(token: string): boolean {
  const payload = parseJWT(token);
  if (!payload) return false;

  const now = Math.floor(Date.now() / 1000);
  const exp = payload.exp;
  const origIat = payload.orig_iat;
  
  // 檢查JWT認證有效時間是否小於290秒
  const timeToExpiry = exp - now;
  if (timeToExpiry >= 290) return false;

  // 檢查使用者最後操作時間是否小於10分鐘
  const lastActivity = get(lastUserActivity);
  if (!lastActivity) return false;
  
  const timeSinceActivity = (Date.now() - lastActivity.getTime()) / 1000;
  if (timeSinceActivity >= 600) return false; // 10分鐘

  // 檢查首次JWT發給時間是否小於11.5小時
  const timeSinceOriginalIssue = now - origIat;
  if (timeSinceOriginalIssue >= 41400) return false; // 11.5小時

  return true;
}

// 檢查JWT是否已過期
export function isTokenExpired(token: string): boolean {
  const payload = parseJWT(token);
  if (!payload) return true;

  const now = Math.floor(Date.now() / 1000);
  return payload.exp < now;
}

// 刷新JWT
export async function refreshToken(): Promise<boolean> {
  try {
    const token = get(authToken);
    if (!token) return false;

    const response = await fetch(`${getApiBaseUrl()}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('刷新權杖失敗');
    }

    const data = await response.json();
    const newToken = data.access_token;

    // 更新token並保存到cookie
    authToken.set(newToken);
    if (browser) {
      Cookies.set('jwt_token', newToken, { expires: 1 });
    }

    return true;
  } catch (error) {
    console.error('刷新權杖失敗:', error);
    return false;
  }
}

// 檢查是否需要重新取得資料
export function shouldRefreshData(): boolean {
  const lastFetch = get(lastDataFetch);
  if (!lastFetch) return true;

  const timeSinceLastFetch = (Date.now() - lastFetch.getTime()) / 1000;
  return timeSinceLastFetch >= 600; // 10分鐘
}

// 定期檢查JWT並自動更新
let tokenCheckInterval: number | null = null;

export async function performPeriodicTokenCheck(): Promise<void> {
  const token = get(authToken);
  if (!token) return;

  try {
    // 檢查JWT是否已過期
    if (isTokenExpired(token)) {
      console.warn('JWT已過期，清除登入狀態並跳轉至登入頁面');
      await logout();
      goto('/login');
      return;
    }

    // 檢查是否需要刷新JWT
    if (checkTokenRefreshNeeded(token)) {
      console.log('JWT即將過期，嘗試刷新');
      const refreshSuccess = await refreshToken();
      if (!refreshSuccess) {
        console.warn('JWT刷新失敗，清除登入狀態');
        await logout();
        goto('/login');
        return;
      }
    }

    // 檢查是否需要刷新資料
    if (shouldRefreshData()) {
      console.log('資料已過期，重新載入');
      try {
        await dataManager.loadPharmaceuticals(true);
        await dataManager.loadTakeRecords(true);
        lastDataFetch.set(new Date());
      } catch (error) {
        console.error('重新載入資料失敗:', error);
      }
    }

  } catch (error) {
    console.error('定期檢查過程中發生錯誤:', error);
  }
}

export function startPeriodicTokenCheck(): void {
  if (tokenCheckInterval) {
    clearInterval(tokenCheckInterval);
  }

  // 每300秒檢查一次
  tokenCheckInterval = setInterval(performPeriodicTokenCheck, 300000);
  
  // 立即執行一次檢查
  performPeriodicTokenCheck();
}

export function stopPeriodicTokenCheck(): void {
  if (tokenCheckInterval) {
    clearInterval(tokenCheckInterval);
    tokenCheckInterval = null;
  }
}

// Cookie 管理
export function saveToken(token: string): void {
  if (browser) {
    Cookies.set('jwt_token', token, { expires: 7 });
    authToken.set(token);
  }
}

export function getStoredToken(): string | null {
  if (browser) {
    return Cookies.get('jwt_token') || null;
  }
  return null;
}

export function clearToken(): void {
  if (browser) {
    Cookies.remove('jwt_token');
    authToken.set(null);
    currentUser.set(null);
  }
}

// API 呼叫函數
export async function login(credentials: LoginRequest): Promise<LoginResponse> {
  try {
    const apiBaseUrl = getApiBaseUrl();
    const response = await fetch(`${apiBaseUrl}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(credentials)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '登入失敗');
    }

    const data = await response.json();
    return LoginResponseSchema.parse(data);
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('登入請求失敗');
  }
}

export async function fetchUserInfo(): Promise<UserInfo> {
  // 使用 DataManager 統一管理
  const userInfo = await dataManager.loadUserInfo();
  if (!userInfo) {
    throw new Error('無法取得使用者資訊');
  }
  return userInfo;
}

export async function callApi(url: string, options: RequestInit = {}): Promise<Response> {
  // 更新使用者操作時間
  updateUserActivity();
  
  const token = get(authToken);
  const baseUrl = getApiBaseUrl();

  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };

  const finalOptions: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers
    }
  };

  try {
    const response = await fetch(`${baseUrl}${url}`, finalOptions);

    if (!response.ok && response.status === 401) {
      // 顯示登出訊息並跳轉到登入頁面
      console.warn('後端回傳認證失效，清除登入狀態');
      clearToken();
      
      // 使用 alert 或其他方式通知用戶
      if (browser) {
        alert('您已登出，請重新登入');
        goto('/login');
      }
      
      throw new Error('認證失效，請重新登入');
    }

    return response;
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('API 呼叫失敗');
  }
}

// 認證檢查和初始化
export async function checkAuthStatus(): Promise<boolean> {
  const token = getStoredToken();

  if (!token) {
    return false;
  }

  if (isTokenExpired(token)) {
    clearToken();
    return false;
  }

  try {
    authToken.set(token);
    const userInfo = await fetchUserInfo();
    currentUser.set(userInfo);
    
    // 初始化使用者操作時間
    updateUserActivity();

    // 使用新的 DataManager 載入使用者資訊
    try {
      await dataManager.loadUserInfo(true);
    } catch (error) {
      console.error('載入使用者資料失敗:', error);
      // 不影響登入流程
    }

    // 啟動定期JWT檢查
    startPeriodicTokenCheck();

    return true;
  } catch (error) {
    clearToken();
    return false;
  }
}

// 登出
export function logout(): void {
  // 停止定期檢查
  stopPeriodicTokenCheck();
  
  clearToken();

  // 清理所有資料
  try {
    dataManager.clearAllData();
  } catch (error) {
    console.error('清理資料失敗:', error);
  }

  goto('/login');
}

// 處理API錯誤
export function handleApiError(error: any): void {
  if (error.message === '認證失效') {
    clearToken();
    goto('/login');
  }
}

// 自動登出倒數計時
export function startLogoutCountdown(seconds: number, onComplete?: () => void): void {
  let countdown = seconds;
  const interval = setInterval(() => {
    countdown--;
    if (countdown <= 0) {
      clearInterval(interval);
      if (onComplete) {
        onComplete();
      } else {
        goto('/login');
      }
    }
  }, 1000);
}
