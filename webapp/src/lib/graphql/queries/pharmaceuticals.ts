// 藥物相關查詢

export const GET_PHARMACEUTICALS = `
  query GetPharmaceuticals($currentPlanOnly: Boolean) {
    getPharmaceuticals(currentPlanOnly: $currentPlanOnly) {
      id
      name
      formulationType
      dosagePerServing
      dosageUnit
      servingUnitName
      currentPlan
      description
      createdBy
      takes {
        id
        quantity
        takenAt
        notes
      }
    }
  }
`;

export const CREATE_PHARMACEUTICAL = `
  mutation CreatePharmaceutical($pharmaceutical: PharmaceuticalInput!) {
    createPharmaceutical(pharmaceutical: $pharmaceutical) {
      id
      name
      formulationType
      dosagePerServing
      dosageUnit
      servingUnitName
      currentPlan
      description
      createdBy
    }
  }
`;

export const UPDATE_PHARMACEUTICAL = `
  mutation UpdatePharmaceutical($pharmaceutical: PharmaceuticalUpdateInput!) {
    updatePharmaceutical(pharmaceutical: $pharmaceutical) {
      id
      name
      formulationType
      dosagePerServing
      dosageUnit
      servingUnitName
      currentPlan
      description
      createdBy
    }
  }
`;

export const DELETE_PHARMACEUTICAL = `
  mutation DeletePharmaceutical($id: Int!) {
    deletePharmaceutical(id: $id)
  }
`;