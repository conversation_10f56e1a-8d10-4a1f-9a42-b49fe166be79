# GraphQL 型別定義與查詢

此目錄包含自動生成的 GraphQL TypeScript 型別定義以及手寫的查詢語句。

## 檔案結構

```
graphql/
├── generated.ts         # 自動生成的 TypeScript 型別定義
├── schema.json         # GraphQL schema 內省結果（JSON 格式）
├── index.ts           # 統一匯出入口
├── queries/           # 手寫的 GraphQL 查詢語句
│   ├── takes.ts       # 服藥記錄相關查詢
│   ├── pharmaceuticals.ts # 藥物相關查詢
│   └── user.ts        # 使用者相關查詢
└── README.md          # 說明文件
```

## 使用方式

### 匯入型別定義

```typescript
import type { 
  GraphQLTakeType, 
  GraphQLPharmaceuticalType,
  FormulationType 
} from '$lib/graphql';
```

### 匯入查詢語句

```typescript
import { GET_TAKES, CREATE_TAKE } from '$lib/graphql';
```

### 匯入所有內容

```typescript
import * as GraphQL from '$lib/graphql';
```

## 重新生成型別定義

當後端 GraphQL schema 有變更時，需要重新生成前端型別定義：

```bash
# 單次生成
pnpm run codegen

# 監聽模式（自動重新生成）
pnpm run codegen:watch
```

## 型別定義特色

- **強型別**: 所有 GraphQL 型別都有對應的 TypeScript 介面
- **自動同步**: 與後端 schema 保持同步
- **運行時內省**: 包含 schema.json 供客戶端工具使用
- **命名前綴**: 所有型別都有 `GraphQL` 前綴以避免命名衝突

## 查詢範例

```typescript
// 使用型別定義
const takeRecord: GraphQLTakeType = {
  id: 1,
  userId: 1,
  pharmaceuticalId: 1,
  quantity: 1.0,
  takenAt: '2025-07-21T10:00:00Z',
  notes: '早餐後服用'
};

// 使用 urql 客戶端進行查詢
import { getUrqlClient } from './client';
import { GET_TAKES } from './queries/takes';

const client = getUrqlClient();
const result = await client.query(GET_TAKES, { limit: 10 });

if (result.error) {
  throw new Error(result.error.message);
}

const takes = result.data?.getTakes as GraphQLTakeType[];
```

---

*此文件自動生成於 2025/7/21*