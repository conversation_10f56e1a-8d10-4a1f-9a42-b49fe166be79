/**
 * urql GraphQL 客戶端設定
 * 最後更新: 2025/7/21
 */

import { createClient, cacheExchange, fetchExchange, type Client } from '@urql/svelte';
import { get } from 'svelte/store';
import { getApiBaseUrl } from '$lib/config';
import { authToken } from '$lib/auth';

/**
 * 建立 urql 客戶端實例
 */
export function createUrqlClient(): Client {
  return createClient({
    url: `${getApiBaseUrl()}/graphql/`,
    exchanges: [
      cacheExchange,
      fetchExchange,
    ],
    fetchOptions: () => {
      const token = get(authToken);
      return {
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
      };
    },
  });
}

// 全域客戶端實例
let client: Client;

/**
 * 取得 urql 客戶端實例（單例模式）
 */
export function getUrqlClient(): Client {
  if (!client) {
    client = createUrqlClient();
  }
  return client;
}

/**
 * 重新建立客戶端（用於認證狀態變更時）
 */
export function resetUrqlClient(): void {
  client = createUrqlClient();
}