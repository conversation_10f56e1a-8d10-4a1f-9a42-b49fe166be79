export type Maybe<T> = T | null | undefined;
export type InputMaybe<T> = T | null | undefined;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export interface Scalars {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** Date with time (isoformat) */
  DateTime: { input: string; output: string; }
}

export type FormulationType =
  | 'CAPSULE'
  | 'INJECTION'
  | 'PATCH'
  | 'PILL'
  | 'POWDER'
  | 'SPRAY'
  | 'SYRUP'
  | 'TOPICAL';

export interface GraphQLMutation {
  __typename?: 'Mutation';
  createPharmaceutical: GraphQLPharmaceuticalType;
  createTake: GraphQLTakeType;
  deletePharmaceutical: Scalars['Boolean']['output'];
  deleteTake: Scalars['Boolean']['output'];
  updatePharmaceutical: GraphQLPharmaceuticalType;
  updateTake: GraphQLTakeType;
}


export interface GraphQLMutationCreatePharmaceuticalArgs {
  pharmaceutical: GraphQLPharmaceuticalInput;
}


export interface GraphQLMutationCreateTakeArgs {
  take: GraphQLTakeInput;
}


export interface GraphQLMutationDeletePharmaceuticalArgs {
  id: Scalars['Int']['input'];
}


export interface GraphQLMutationDeleteTakeArgs {
  takeId: Scalars['Int']['input'];
}


export interface GraphQLMutationUpdatePharmaceuticalArgs {
  pharmaceutical: GraphQLPharmaceuticalUpdateInput;
}


export interface GraphQLMutationUpdateTakeArgs {
  take: GraphQLTakeUpdateInput;
}

export interface GraphQLPharmaceuticalInput {
  currentPlan?: InputMaybe<Scalars['Boolean']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  dosagePerServing: Scalars['Float']['input'];
  dosageUnit: Scalars['String']['input'];
  formulationType: FormulationType;
  name: Scalars['String']['input'];
  servingUnitName?: InputMaybe<Scalars['String']['input']>;
}

export interface GraphQLPharmaceuticalType {
  __typename?: 'PharmaceuticalType';
  createdBy: Scalars['Int']['output'];
  currentPlan: Scalars['Boolean']['output'];
  description?: Maybe<Scalars['String']['output']>;
  dosagePerServing: Scalars['Float']['output'];
  dosageUnit: Scalars['String']['output'];
  formulationType: FormulationType;
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  servingUnitName?: Maybe<Scalars['String']['output']>;
  takes: Array<GraphQLTakeType>;
}

export interface GraphQLPharmaceuticalUpdateInput {
  currentPlan?: InputMaybe<Scalars['Boolean']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  dosagePerServing?: InputMaybe<Scalars['Float']['input']>;
  dosageUnit?: InputMaybe<Scalars['String']['input']>;
  formulationType?: InputMaybe<FormulationType>;
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  servingUnitName?: InputMaybe<Scalars['String']['input']>;
}

export interface GraphQLQuery {
  __typename?: 'Query';
  getPharmaceuticals: Array<GraphQLPharmaceuticalType>;
  getTakes: Array<GraphQLTakeType>;
  getUser: GraphQLUserType;
}


export interface GraphQLQueryGetPharmaceuticalsArgs {
  currentPlanOnly?: InputMaybe<Scalars['Boolean']['input']>;
}


export interface GraphQLQueryGetTakesArgs {
  fromTime?: InputMaybe<Scalars['DateTime']['input']>;
  limit?: Scalars['Int']['input'];
  offset?: Scalars['Int']['input'];
  pharmaceuticalId?: InputMaybe<Scalars['Int']['input']>;
  toTime?: InputMaybe<Scalars['DateTime']['input']>;
}

export interface GraphQLTakeInput {
  notes?: InputMaybe<Scalars['String']['input']>;
  pharmaceuticalId: Scalars['Int']['input'];
  quantity: Scalars['Float']['input'];
  takenAt: Scalars['DateTime']['input'];
  userId: Scalars['Int']['input'];
}

export interface GraphQLTakeType {
  __typename?: 'TakeType';
  id: Scalars['Int']['output'];
  notes?: Maybe<Scalars['String']['output']>;
  pharmaceutical?: Maybe<GraphQLPharmaceuticalType>;
  pharmaceuticalId: Scalars['Int']['output'];
  quantity: Scalars['Float']['output'];
  takenAt: Scalars['DateTime']['output'];
  user?: Maybe<GraphQLUserType>;
  userId: Scalars['Int']['output'];
}

export interface GraphQLTakeUpdateInput {
  id: Scalars['Int']['input'];
  notes?: InputMaybe<Scalars['String']['input']>;
  quantity?: InputMaybe<Scalars['Float']['input']>;
  takenAt?: InputMaybe<Scalars['DateTime']['input']>;
}

export interface GraphQLUserType {
  __typename?: 'UserType';
  email: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  takes: Array<GraphQLTakeType>;
}
