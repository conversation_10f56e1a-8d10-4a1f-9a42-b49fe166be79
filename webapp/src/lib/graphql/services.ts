/**
 * GraphQL 服務層 - 使用 urql 替代原生 fetch
 * 最後更新: 2025/7/21
 */

import { getUrqlClient } from './client';
import { GET_TAKES, GET_PHARMACEUTICALS, GET_USER } from './queries/index';
import type { 
  GraphQLTakeType, 
  GraphQLPharmaceuticalType, 
  GraphQLUserType,
  GraphQLQueryGetTakesArgs,
  GraphQLQueryGetPharmaceuticalsArgs 
} from './generated';

/**
 * 查詢服藥記錄
 */
export async function queryTakes(variables: GraphQLQueryGetTakesArgs = {}) {
  const client = getUrqlClient();
  const result = await client.query(GET_TAKES, variables);
  
  if (result.error) {
    throw new Error(result.error.message);
  }
  
  return result.data?.getTakes as GraphQLTakeType[];
}

/**
 * 查詢藥物清單
 */
export async function queryPharmaceuticals(variables: GraphQLQueryGetPharmaceuticalsArgs = {}) {
  const client = getUrqlClient();
  const result = await client.query(GET_PHARMACEUTICALS, variables);
  
  if (result.error) {
    throw new Error(result.error.message);
  }
  
  return result.data?.getPharmaceuticals as GraphQLPharmaceuticalType[];
}

/**
 * 查詢當前使用者資訊
 */
export async function queryUser() {
  const client = getUrqlClient();
  const result = await client.query(GET_USER, {});
  
  if (result.error) {
    throw new Error(result.error.message);
  }
  
  return result.data?.getUser as GraphQLUserType;
}

/**
 * 建立服藥記錄
 */
export async function createTake(variables: { take: any }) {
  const client = getUrqlClient();
  const CREATE_TAKE = `
    mutation CreateTake($take: TakeInput!) {
      createTake(take: $take) {
        id
        userId
        pharmaceuticalId
        quantity
        takenAt
        notes
        pharmaceutical {
          id
          name
          dosageUnit
          dosagePerServing
          servingUnitName
        }
      }
    }
  `;
  
  const result = await client.mutation(CREATE_TAKE, variables);
  
  if (result.error) {
    throw new Error(result.error.message);
  }
  
  return result.data?.createTake as GraphQLTakeType;
}

/**
 * 更新服藥記錄
 */
export async function updateTake(variables: { take: any }) {
  const client = getUrqlClient();
  const UPDATE_TAKE = `
    mutation UpdateTake($take: TakeUpdateInput!) {
      updateTake(take: $take) {
        id
        userId
        pharmaceuticalId
        quantity
        takenAt
        notes
      }
    }
  `;
  
  const result = await client.mutation(UPDATE_TAKE, variables);
  
  if (result.error) {
    throw new Error(result.error.message);
  }
  
  return result.data?.updateTake as GraphQLTakeType;
}

/**
 * 刪除服藥記錄
 */
export async function deleteTake(variables: { takeId: number }) {
  const client = getUrqlClient();
  const DELETE_TAKE = `
    mutation DeleteTake($takeId: Int!) {
      deleteTake(takeId: $takeId)
    }
  `;
  
  const result = await client.mutation(DELETE_TAKE, variables);
  
  if (result.error) {
    throw new Error(result.error.message);
  }
  
  return result.data?.deleteTake as boolean;
}

/**
 * 建立藥物
 */
export async function createPharmaceutical(variables: { pharmaceutical: any }) {
  const client = getUrqlClient();
  const CREATE_PHARMACEUTICAL = `
    mutation CreatePharmaceutical($pharmaceutical: PharmaceuticalInput!) {
      createPharmaceutical(pharmaceutical: $pharmaceutical) {
        id
        name
        formulationType
        dosagePerServing
        dosageUnit
        servingUnitName
        currentPlan
        description
        createdBy
      }
    }
  `;
  
  const result = await client.mutation(CREATE_PHARMACEUTICAL, variables);
  
  if (result.error) {
    throw new Error(result.error.message);
  }
  
  return result.data?.createPharmaceutical as GraphQLPharmaceuticalType;
}

/**
 * 刪除藥物
 */
export async function deletePharmaceutical(variables: { id: number }) {
  const client = getUrqlClient();
  const DELETE_PHARMACEUTICAL = `
    mutation DeletePharmaceutical($id: Int!) {
      deletePharmaceutical(id: $id)
    }
  `;
  
  const result = await client.mutation(DELETE_PHARMACEUTICAL, variables);
  
  if (result.error) {
    throw new Error(result.error.message);
  }
  
  return result.data?.deletePharmaceutical as boolean;
}