/**
 * 統一資料狀態管理 Store
 * 最後更新: 2025/7/21
 */

import { writable, derived, get } from 'svelte/store';
import type { UserInfo } from '$lib/auth';
import type { PharmaceuticalTakeRecord, AvailablePharmaceutical, QuickPharmaceuticalOption } from '$lib/types/pharmaceutical';
import { LoadingState, type DataState } from '$lib/types/loadingTypes';

// 全域載入狀態
interface GlobalDataState {
  userInfo: DataState<UserInfo>;
  pharmaceuticals: DataState<AvailablePharmaceutical[]>;
  takeRecords: DataState<PharmaceuticalTakeRecord[]>;
  quickOptions: DataState<QuickPharmaceuticalOption[]>;
  lastGlobalUpdate: Date | null;
}

// 初始狀態
const initialState: GlobalDataState = {
  userInfo: {
    data: null,
    loading: {
      state: LoadingState.NOT_LOADED,
      lastUpdate: undefined,
      lastAttempt: undefined,
      retryCount: 0
    }
  },
  pharmaceuticals: {
    data: null,
    loading: {
      state: LoadingState.NOT_LOADED,
      lastUpdate: undefined,
      lastAttempt: undefined,
      retryCount: 0
    }
  },
  takeRecords: {
    data: null,
    loading: {
      state: LoadingState.NOT_LOADED,
      lastUpdate: undefined,
      lastAttempt: undefined,
      retryCount: 0
    }
  },
  quickOptions: {
    data: null,
    loading: {
      state: LoadingState.NOT_LOADED,
      lastUpdate: undefined,
      lastAttempt: undefined,
      retryCount: 0
    }
  },
  lastGlobalUpdate: null
};

// 主要資料 Store
export const dataStore = writable<GlobalDataState>(initialState);

// 衍生 Store - 用戶資訊
export const userInfoStore = derived(
  dataStore,
  ($dataStore) => $dataStore.userInfo
);

// 衍生 Store - 藥物清單
export const pharmaceuticalsStore = derived(
  dataStore,
  ($dataStore) => $dataStore.pharmaceuticals
);

// 衍生 Store - 服藥記錄
export const takeRecordsStore = derived(
  dataStore,
  ($dataStore) => $dataStore.takeRecords
);

// 衍生 Store - 快速選項
export const quickOptionsStore = derived(
  dataStore,
  ($dataStore) => $dataStore.quickOptions
);

// 衍生 Store - 全域載入狀態
export const globalLoadingStore = derived(
  dataStore,
  ($dataStore) => ({
    isUserInfoLoading: $dataStore.userInfo.loading.state === LoadingState.LOADING,
    isPharmaceuticalsLoading: $dataStore.pharmaceuticals.loading.state === LoadingState.LOADING,
    isTakeRecordsLoading: $dataStore.takeRecords.loading.state === LoadingState.LOADING,
    isQuickOptionsLoading: $dataStore.quickOptions.loading.state === LoadingState.LOADING,
    isAnyLoading: 
      $dataStore.userInfo.loading.state === LoadingState.LOADING ||
      $dataStore.pharmaceuticals.loading.state === LoadingState.LOADING ||
      $dataStore.takeRecords.loading.state === LoadingState.LOADING ||
      $dataStore.quickOptions.loading.state === LoadingState.LOADING,
    hasAnyError:
      $dataStore.userInfo.loading.state === LoadingState.ERROR ||
      $dataStore.pharmaceuticals.loading.state === LoadingState.ERROR ||
      $dataStore.takeRecords.loading.state === LoadingState.ERROR ||
      $dataStore.quickOptions.loading.state === LoadingState.ERROR,
    lastGlobalUpdate: $dataStore.lastGlobalUpdate
  })
);

// Store 操作函數
export const dataStoreActions = {
  // 設定用戶資訊載入狀態
  setUserInfoLoading(state: LoadingState, error?: string): void {
    dataStore.update(store => ({
      ...store,
      userInfo: {
        ...store.userInfo,
        loading: {
          ...store.userInfo.loading,
          state,
          error,
          lastAttempt: new Date(),
          retryCount: state === LoadingState.LOADING ? 0 : store.userInfo.loading.retryCount
        }
      }
    }));
  },

  // 設定用戶資訊資料
  setUserInfoData(data: UserInfo | null): void {
    dataStore.update(store => ({
      ...store,
      userInfo: {
        data,
        loading: {
          ...store.userInfo.loading,
          state: data ? LoadingState.LOADED : LoadingState.NOT_LOADED,
          lastUpdate: data ? new Date() : undefined,
          error: undefined
        }
      },
      lastGlobalUpdate: new Date()
    }));
  },

  // 設定藥物清單載入狀態
  setPharmaceuticalsLoading(state: LoadingState, error?: string): void {
    dataStore.update(store => ({
      ...store,
      pharmaceuticals: {
        ...store.pharmaceuticals,
        loading: {
          ...store.pharmaceuticals.loading,
          state,
          error,
          lastAttempt: new Date(),
          retryCount: state === LoadingState.LOADING ? 0 : store.pharmaceuticals.loading.retryCount
        }
      }
    }));
  },

  // 設定藥物清單資料
  setPharmaceuticalsData(data: AvailablePharmaceutical[] | null): void {
    dataStore.update(store => ({
      ...store,
      pharmaceuticals: {
        data,
        loading: {
          ...store.pharmaceuticals.loading,
          state: data ? LoadingState.LOADED : LoadingState.NOT_LOADED,
          lastUpdate: data ? new Date() : undefined,
          error: undefined
        }
      },
      lastGlobalUpdate: new Date()
    }));
  },

  // 設定服藥記錄載入狀態
  setTakeRecordsLoading(state: LoadingState, error?: string): void {
    dataStore.update(store => ({
      ...store,
      takeRecords: {
        ...store.takeRecords,
        loading: {
          ...store.takeRecords.loading,
          state,
          error,
          lastAttempt: new Date(),
          retryCount: state === LoadingState.LOADING ? 0 : store.takeRecords.loading.retryCount
        }
      }
    }));
  },

  // 設定服藥記錄資料
  setTakeRecordsData(data: PharmaceuticalTakeRecord[] | null): void {
    dataStore.update(store => ({
      ...store,
      takeRecords: {
        data,
        loading: {
          ...store.takeRecords.loading,
          state: data ? LoadingState.LOADED : LoadingState.NOT_LOADED,
          lastUpdate: data ? new Date() : undefined,
          error: undefined
        }
      },
      lastGlobalUpdate: new Date()
    }));
  },

  // 設定快速選項載入狀態
  setQuickOptionsLoading(state: LoadingState, error?: string): void {
    dataStore.update(store => ({
      ...store,
      quickOptions: {
        ...store.quickOptions,
        loading: {
          ...store.quickOptions.loading,
          state,
          error,
          lastAttempt: new Date(),
          retryCount: state === LoadingState.LOADING ? 0 : store.quickOptions.loading.retryCount
        }
      }
    }));
  },

  // 設定快速選項資料
  setQuickOptionsData(data: QuickPharmaceuticalOption[] | null): void {
    dataStore.update(store => ({
      ...store,
      quickOptions: {
        data,
        loading: {
          ...store.quickOptions.loading,
          state: data ? LoadingState.LOADED : LoadingState.NOT_LOADED,
          lastUpdate: data ? new Date() : undefined,
          error: undefined
        }
      },
      lastGlobalUpdate: new Date()
    }));
  },

  // 增加重試次數
  incrementRetryCount(dataType: 'userInfo' | 'pharmaceuticals' | 'takeRecords' | 'quickOptions'): void {
    dataStore.update(store => ({
      ...store,
      [dataType]: {
        ...store[dataType],
        loading: {
          ...store[dataType].loading,
          retryCount: (store[dataType].loading.retryCount || 0) + 1
        }
      }
    }));
  },

  // 清除所有資料
  clearAllData(): void {
    dataStore.set(initialState);
  },

  // 檢查資料是否需要刷新（基於時間）
  needsRefresh(dataType: 'userInfo' | 'pharmaceuticals' | 'takeRecords' | 'quickOptions', maxAgeMs: number = 600000): boolean {
    const store = get(dataStore);
    const dataState = store[dataType];
    
    if (dataState.loading.state === LoadingState.NOT_LOADED) {
      return true;
    }

    if (!dataState.loading.lastUpdate) {
      return true;
    }

    const ageMs = Date.now() - dataState.loading.lastUpdate.getTime();
    return ageMs > maxAgeMs;
  },

  // 檢查是否正在載入
  isLoading(dataType: 'userInfo' | 'pharmaceuticals' | 'takeRecords' | 'quickOptions'): boolean {
    const store = get(dataStore);
    const state = store[dataType].loading.state;
    return state === LoadingState.LOADING || state === LoadingState.REFRESHING;
  },

  // 檢查是否有資料
  hasData(dataType: 'userInfo' | 'pharmaceuticals' | 'takeRecords' | 'quickOptions'): boolean {
    const store = get(dataStore);
    return store[dataType].data !== null;
  }
};

// 工具函數：生成快速選項
export function generateQuickPharmaceuticalOptions(
  records: PharmaceuticalTakeRecord[], 
  pharmaceuticals: AvailablePharmaceutical[]
): QuickPharmaceuticalOption[] {
  // 取得治療中的藥物 ID 集合
  const currentPlanPharmaceuticals = new Set(
    pharmaceuticals.filter(p => p.currentPlan).map(p => p.id)
  );
  
  // 過濾只包含治療中的藥物
  const authorizedRecords = records.filter(record => 
    currentPlanPharmaceuticals.has(record.pharmaceuticalId)
  );
  
  const pharmaceuticalGroups = new Map<string, {
    pharmaceuticalId: number;
    pharmaceuticalName: string;
    dosage: number;
    dosageUnit: string;
    records: PharmaceuticalTakeRecord[];
  }>();

  authorizedRecords.forEach(record => {
    const key = `${record.pharmaceuticalId}-${record.dosage}`;

    if (!pharmaceuticalGroups.has(key)) {
      pharmaceuticalGroups.set(key, {
        pharmaceuticalId: record.pharmaceuticalId,
        pharmaceuticalName: record.pharmaceuticalName,
        dosage: record.dosage,
        dosageUnit: record.dosageUnit,
        records: []
      });
    }

    pharmaceuticalGroups.get(key)!.records.push(record);
  });

  const pharmaceuticalMap = new Map<number, {
    pharmaceuticalId: number;
    pharmaceuticalName: string;
    bestDosage: number;
    dosageUnit: string;
    maxCount: number;
    todayCount: number;
    lastTakenAt: string;
  }>();

  for (const [key, group] of pharmaceuticalGroups) {
    const count = group.records.length;
    
    // 計算當日服用次數
    const today = new Date().toDateString();
    const todayCount = group.records.filter(record => {
      const recordDate = new Date(record.takenAt).toDateString();
      return recordDate === today;
    }).length;
    
    // 取得最後服用時間
    const sortedRecords = group.records.sort((a, b) => 
      new Date(b.takenAt).getTime() - new Date(a.takenAt).getTime()
    );
    const lastTakenAt = sortedRecords[0]?.takenAt || '';

    const existing = pharmaceuticalMap.get(group.pharmaceuticalId);
    if (!existing || count > existing.maxCount) {
      pharmaceuticalMap.set(group.pharmaceuticalId, {
        pharmaceuticalId: group.pharmaceuticalId,
        pharmaceuticalName: group.pharmaceuticalName,
        bestDosage: group.dosage,
        dosageUnit: group.dosageUnit,
        maxCount: count,
        todayCount,
        lastTakenAt
      });
    }
  }

  // 轉換為快速選項格式
  return Array.from(pharmaceuticalMap.values())
    .map(item => ({
      pharmaceuticalId: item.pharmaceuticalId,
      pharmaceuticalName: item.pharmaceuticalName,
      dosage: item.bestDosage,
      dosageUnit: item.dosageUnit,
      count: item.maxCount,
      todayCount: item.todayCount,
      lastTakenAt: item.lastTakenAt
    }))
    .sort((a, b) => b.count - a.count);
}