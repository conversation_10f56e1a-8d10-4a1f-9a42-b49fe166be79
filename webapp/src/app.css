@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 主題切換動畫 */
:root {
  --transition-theme: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

* {
  transition: var(--transition-theme);
}

/* 防止閃爍 */
html {
  color-scheme: light dark;
}

html.dark {
  color-scheme: dark;
}

html.light {
  color-scheme: light;
}

@layer components {
  /* 卡片樣式 */
  .card {
    @apply bg-white dark:bg-surface-800 rounded-xl shadow-lg border border-surface-200 dark:border-surface-700;
  }

  /* 按鈕樣式 */
  .btn {
    @apply inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow-md transform hover:-translate-y-0.5;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 text-white hover:from-primary-700 hover:to-primary-800 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-secondary-600 to-secondary-700 text-white hover:from-secondary-700 hover:to-secondary-800 focus:ring-secondary-500;
  }

  .btn-success {
    @apply bg-gradient-to-r from-success-600 to-success-700 text-white hover:from-success-700 hover:to-success-800 focus:ring-success-500;
  }

  .btn-warning {
    @apply bg-gradient-to-r from-warning-600 to-warning-700 text-white hover:from-warning-700 hover:to-warning-800 focus:ring-warning-500;
  }

  .btn-error {
    @apply bg-gradient-to-r from-error-600 to-error-700 text-white hover:from-error-700 hover:to-error-800 focus:ring-error-500;
  }

  .btn-outline {
    @apply border-2 bg-transparent hover:bg-surface-50 dark:hover:bg-surface-800;
  }

  .btn-outline-primary {
    @apply border-primary-600 text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900/20;
  }

  .btn-ghost {
    @apply bg-transparent hover:bg-surface-100 dark:hover:bg-surface-800;
  }

  /* 表單元素 */
  .input {
    @apply block w-full px-3 py-2 border border-surface-300 dark:border-surface-600 rounded-lg bg-white dark:bg-surface-800 text-surface-900 dark:text-surface-100 placeholder-surface-500 focus:border-primary-500 focus:ring-1 focus:ring-primary-500;
  }

  .label {
    @apply block;
  }

  .label-text {
    @apply block text-sm font-medium text-surface-700 dark:text-surface-300 mb-1;
  }

  /* 警告框 */
  .alert {
    @apply p-4 rounded-lg flex items-center gap-3;
  }

  .alert-success {
    @apply bg-success-50 border border-success-200 text-success-800 dark:bg-success-900/20 dark:border-success-800 dark:text-success-200;
  }

  .alert-warning {
    @apply bg-warning-50 border border-warning-200 text-warning-800 dark:bg-warning-900/20 dark:border-warning-800 dark:text-warning-200;
  }

  .alert-error {
    @apply bg-error-50 border border-error-200 text-error-800 dark:bg-error-900/20 dark:border-error-800 dark:text-error-200;
  }

  /* 徽章 */
  .badge {
    @apply inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full;
  }

  .badge-success {
    @apply bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-400;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800 dark:bg-warning-900/30 dark:text-warning-400;
  }

  .badge-error {
    @apply bg-error-100 text-error-800 dark:bg-error-900/30 dark:text-error-400;
  }

  /* 圖示按鈕 */
  .btn-icon {
    @apply inline-flex items-center justify-center w-10 h-10 rounded-full transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-0.5;
  }

  /* 標題樣式 */
  .h1 {
    @apply text-4xl font-bold tracking-tight;
  }

  .h2 {
    @apply text-3xl font-bold tracking-tight;
  }

  .h3 {
    @apply text-2xl font-semibold tracking-tight;
  }

  .h4 {
    @apply text-xl font-semibold tracking-tight;
  }

  /* 展開/折疊動畫類別 */
  .expand-enter {
    animation: slideDown 0.3s ease-out forwards;
  }

  .expand-exit {
    animation: slideUp 0.3s ease-in forwards;
  }

  .fade-enter {
    animation: fadeIn 0.2s ease-out forwards;
  }

  .fade-exit {
    animation: fadeOut 0.2s ease-in forwards;
  }

  /* 預設隱藏狀態 */
  .collapsible {
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .collapsible-enter {
    max-height: 0;
    opacity: 0;
  }

  .collapsible-enter-active {
    max-height: 1000px;
    opacity: 1;
  }

  .collapsible-exit {
    max-height: 1000px;
    opacity: 1;
  }

  .collapsible-exit-active {
    max-height: 0;
    opacity: 0;
  }
}

/* 展開/折疊動畫關鍵幀 */
@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 1000px;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 1;
    max-height: 1000px;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}