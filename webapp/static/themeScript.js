/**
 * 防止主題閃爍的腳本
 * 此腳本會在頁面載入前執行，避免使用者看到短暫的錯誤主題
 * 
 * 注意：此文件必須是純 JavaScript，因為它會在 SvelteKit 應用載入前執行
 */

(function() {
  // 預設主題值
  const DEFAULT_THEME = 'auto';
  
  // 支援的主題選項
  const THEME_OPTIONS = ['light', 'dark', 'auto'];
  
  /**
   * 從 localStorage 讀取儲存的主題設定
   */
  function getStoredTheme() {
    try {
      const stored = localStorage.getItem('theme');
      if (stored && THEME_OPTIONS.includes(stored)) {
        return stored;
      }
    } catch (error) {
      // localStorage 不可用
    }
    return DEFAULT_THEME;
  }
  
  /**
   * 檢測系統主題偏好
   */
  function getSystemTheme() {
    return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches 
      ? 'dark' 
      : 'light';
  }
  
  /**
   * 計算實際應用的主題
   */
  function getEffectiveTheme(theme) {
    if (theme === 'auto') {
      return getSystemTheme();
    }
    return theme;
  }
  
  /**
   * 應用主題到 DOM
   */
  function applyTheme(theme) {
    const effectiveTheme = getEffectiveTheme(theme);
    const root = document.documentElement;
    
    // 移除舊的主題類別
    root.classList.remove('light', 'dark');
    
    // 添加新的主題類別
    root.classList.add(effectiveTheme);
  }
  
  // 立即應用主題
  const storedTheme = getStoredTheme();
  applyTheme(storedTheme);
})();