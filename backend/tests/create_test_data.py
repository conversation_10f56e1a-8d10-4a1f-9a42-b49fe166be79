#!/usr/bin/env python3
"""
建立測試資料庫和虛假資料的腳本
"""

import random
import sys
from decimal import Decimal
from pathlib import Path

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent.parent))

from faker import Faker
from sqlmodel import Session, create_engine

from when_did_i_take.auth import get_password_hash
from when_did_i_take.models import FormulationType, Pharmaceutical, Take, User

# 設定 Faker 為繁體中文
fake = Faker('zh_TW')

# 測試資料庫路徑
TEST_DB_PATH = Path(__file__).parent / "test_data.db"


def create_test_database():
    """建立測試資料庫"""
    # 如果測試資料庫已存在，先刪除
    if TEST_DB_PATH.exists():
        TEST_DB_PATH.unlink()
        print(f"已刪除舊的測試資料庫: {TEST_DB_PATH}")

    # 建立新的測試資料庫
    database_url = f"sqlite:///{TEST_DB_PATH}"
    engine = create_engine(database_url, echo=False)

    # 建立所有資料表
    from when_did_i_take.models import SQLModel
    SQLModel.metadata.create_all(engine)

    print(f"已建立測試資料庫: {TEST_DB_PATH}")
    return engine


def create_fake_users(session: Session, count: int = 10) -> list[User]:
    """建立虛假使用者資料"""
    users = []

    for _ in range(count):
        # 為測試方便，所有使用者的密碼都設為 "password123"
        password_hash = get_password_hash("password123")

        user = User(
            name=fake.name(),
            email=fake.email(),
            password_hash=password_hash,
            created_at=fake.date_time_between(start_date='-2y', end_date='now')
        )
        session.add(user)
        users.append(user)

    session.commit()
    print(f"已建立 {count} 個虛假使用者")
    return users


def create_fake_pharmaceuticals(session: Session, users: list[User], count: int = 20) -> list[Pharmaceutical]:
    """建立虛假藥物資料 - 確保每個使用者都有至少2個藥物"""
    pharmaceuticals = []

    # 常見藥物名稱
    drug_names = [
        "普拿疼", "阿斯匹靈", "布洛芬", "撲熱息痛", "維他命C",
        "維他命D", "鈣片", "魚油", "益生菌", "葉酸",
        "鐵劑", "B群", "維他命E", "葡萄糖胺", "膠原蛋白",
        "感冒糖漿", "止咳藥", "胃藥", "降血壓藥", "降血糖藥",
        "鎂片", "鋅片", "蔓越莓", "靈芝", "薑黃"
    ]

    # 首先確保每個使用者至少有2個藥物
    min_per_user = 2
    guaranteed_count = len(users) * min_per_user
    
    drug_index = 0
    
    # 為每個使用者建立至少2個藥物
    for user in users:
        for _ in range(min_per_user):
            if drug_index >= len(drug_names):
                drug_index = 0  # 重複使用藥物名稱
                
            name = drug_names[drug_index]
            drug_index += 1
            
            user_specific_name = f"{name} (用戶{user.id})"
            
            pharmaceutical = Pharmaceutical(
                name=user_specific_name,
                formulation_type=fake.random_element(elements=list(FormulationType)),
                dosage_per_serving=Decimal(str(round(random.uniform(0.5, 500.0), 2))),
                dosage_unit=fake.random_element(elements=["mg", "g", "ml", "IU", "mcg"]),
                serving_unit_name=fake.random_element(elements=["顆", "錠", "包", "滴", "cc", None]),
                current_plan=fake.boolean(chance_of_getting_true=80),  # 80% 機率為治療中
                description=fake.text(max_nb_chars=200),
                created_by=user.id,
                created_at=fake.date_time_between(start_date='-1y', end_date='now')
            )
            session.add(pharmaceutical)
            pharmaceuticals.append(pharmaceutical)
    
    # 如果還需要更多藥物，隨機分配給使用者
    remaining_count = count - guaranteed_count
    if remaining_count > 0:
        for _ in range(remaining_count):
            if drug_index >= len(drug_names):
                drug_index = 0
                
            name = drug_names[drug_index]
            drug_index += 1
            
            creator = fake.random_element(elements=users)
            user_specific_name = f"{name} (用戶{creator.id})"
            
            pharmaceutical = Pharmaceutical(
                name=user_specific_name,
                formulation_type=fake.random_element(elements=list(FormulationType)),
                dosage_per_serving=Decimal(str(round(random.uniform(0.5, 500.0), 2))),
                dosage_unit=fake.random_element(elements=["mg", "g", "ml", "IU", "mcg"]),
                serving_unit_name=fake.random_element(elements=["顆", "錠", "包", "滴", "cc", None]),
                current_plan=fake.boolean(chance_of_getting_true=80),  # 80% 機率為治療中
                description=fake.text(max_nb_chars=200),
                created_by=creator.id,
                created_at=fake.date_time_between(start_date='-1y', end_date='now')
            )
            session.add(pharmaceutical)
            pharmaceuticals.append(pharmaceutical)

    session.commit()
    print(f"已建立 {len(pharmaceuticals)} 個虛假藥物（每個使用者至少{min_per_user}個）")
    return pharmaceuticals


def create_fake_takes(session: Session, users: list[User], pharmaceuticals: list[Pharmaceutical], count: int = 100) -> list[Take]:
    """建立虛假服藥記錄 - 確保使用者只能服用自己建立的藥物"""
    takes = []

    # 為每個使用者建立藥物對應表
    user_pharmaceuticals = {}
    for pharmaceutical in pharmaceuticals:
        user_id = pharmaceutical.created_by
        if user_id not in user_pharmaceuticals:
            user_pharmaceuticals[user_id] = []
        user_pharmaceuticals[user_id].append(pharmaceutical)

    attempts = 0
    max_attempts = count * 3  # 避免無限迴圈

    while len(takes) < count and attempts < max_attempts:
        attempts += 1
        
        # 隨機選擇一個使用者
        user = fake.random_element(elements=users)
        
        # 檢查該使用者是否有自己建立的藥物
        if user.id not in user_pharmaceuticals or not user_pharmaceuticals[user.id]:
            continue
            
        # 只能選擇該使用者自己建立的藥物
        pharmaceutical = fake.random_element(elements=user_pharmaceuticals[user.id])

        # 生成過去30天內的隨機時間
        taken_at = fake.date_time_between(start_date='-30d', end_date='now')

        take = Take(
            user_id=user.id,
            pharmaceutical_id=pharmaceutical.id,
            quantity=Decimal(str(round(random.uniform(0.5, 5.0), 1))),
            taken_at=taken_at,
            notes=fake.text(max_nb_chars=100) if fake.boolean(chance_of_getting_true=30) else None
        )
        session.add(take)
        takes.append(take)

    session.commit()
    print(f"已建立 {len(takes)} 個虛假服藥記錄（使用者只服用自己建立的藥物）")
    return takes


def print_database_stats(session: Session):
    """顯示資料庫統計資訊"""
    from sqlmodel import select

    user_count = len(session.exec(select(User)).all())
    pharmaceutical_count = len(session.exec(select(Pharmaceutical)).all())
    take_count = len(session.exec(select(Take)).all())

    print("\n=== 測試資料庫統計 ===")
    print(f"使用者數量: {user_count}")
    print(f"藥物數量: {pharmaceutical_count}")
    print(f"服藥記錄數量: {take_count}")
    print(f"資料庫檔案大小: {TEST_DB_PATH.stat().st_size / 1024:.2f} KB")


def validate_data_integrity(session: Session):
    """驗證資料完整性 - 確保使用者只服用自己建立的藥物"""
    from sqlmodel import select
    
    print("\n=== 資料完整性驗證 ===")
    
    # 檢查每個服藥記錄是否符合邏輯
    takes = session.exec(select(Take)).all()
    pharmaceuticals = {p.id: p for p in session.exec(select(Pharmaceutical)).all()}
    
    violations = 0
    for take in takes:
        pharmaceutical = pharmaceuticals.get(take.pharmaceutical_id)
        if pharmaceutical and pharmaceutical.created_by != take.user_id:
            violations += 1
            print(f"❌ 違規記錄: 使用者{take.user_id}服用了使用者{pharmaceutical.created_by}建立的藥物{pharmaceutical.name}")
    
    if violations == 0:
        print("✅ 所有服藥記錄都符合邏輯：使用者只服用自己建立的藥物")
    else:
        print(f"❌ 發現 {violations} 個違規記錄")
    
    # 統計每個使用者的藥物和服藥記錄
    users = session.exec(select(User)).all()
    for user in users:
        user_pharms = [p for p in pharmaceuticals.values() if p.created_by == user.id]
        user_takes = [t for t in takes if t.user_id == user.id]
        current_plan_pharms = [p for p in user_pharms if p.current_plan]
        
        print(f"使用者{user.id} ({user.name}): {len(user_pharms)}個藥物, {len(current_plan_pharms)}個治療中, {len(user_takes)}次服藥記錄")


def main():
    """主函數"""
    print("開始建立測試資料...")

    # 建立測試資料庫
    engine = create_test_database()

    with Session(engine) as session:
        # 建立虛假資料
        users = create_fake_users(session, count=5)  # 減少使用者數量，便於測試
        pharmaceuticals = create_fake_pharmaceuticals(session, users, count=20)  # 每人平均4個藥物
        takes = create_fake_takes(session, users, pharmaceuticals, count=60)  # 每人平均12次服藥記錄

        # 顯示統計資訊
        print_database_stats(session)
        
        # 驗證資料完整性
        validate_data_integrity(session)

    print(f"\n測試資料建立完成！資料庫位置: {TEST_DB_PATH}")


if __name__ == "__main__":
    main()
