#!/usr/bin/env python3
"""
測試模型定義
"""

import sys
from datetime import datetime
from decimal import Decimal
from pathlib import Path

import pytest

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent.parent))

from when_did_i_take.models import FormulationType, Pharmaceutical, Take, User


class TestFormulationType:
    """測試 FormulationType 枚舉"""

    def test_formulation_type_values(self):
        """測試枚舉值"""
        assert FormulationType.CAPSULE == "Capsule"
        assert FormulationType.PILL == "Pill"
        assert FormulationType.INJECTION == "Injection"
        assert FormulationType.POWDER == "Powder"
        assert FormulationType.SYRUP == "Syrup"
        assert FormulationType.PATCH == "Patch"
        assert FormulationType.SPRAY == "Spray"
        assert FormulationType.TOPICAL == "Topical"

    def test_formulation_type_list(self):
        """測試所有枚舉值"""
        expected_values = [
            "Capsule", "Pill", "Injection", "Powder",
            "Syrup", "Patch", "Spray", "Topical"
        ]
        actual_values = [ft.value for ft in FormulationType]
        assert set(actual_values) == set(expected_values)


class TestUser:
    """測試 User 模型"""

    def test_user_creation(self):
        """測試使用者建立"""
        user = User(
            name="測試使用者",
            email="<EMAIL>",
            created_at=datetime.now()
        )

        assert user.name == "測試使用者"
        assert user.email == "<EMAIL>"
        assert user.created_at is not None
        assert user.id is None  # 尚未儲存到資料庫

    def test_user_required_fields(self):
        """測試必要欄位"""
        # 測試缺少 name
        with pytest.raises(Exception):
            user = User(email="<EMAIL>")
            user.model_validate(user.model_dump())

        # 測試缺少 email
        with pytest.raises(Exception):
            user = User(name="測試使用者")
            user.model_validate(user.model_dump())

    def test_user_optional_fields(self):
        """測試可選欄位"""
        user = User(
            name="測試使用者",
            email="<EMAIL>"
            # created_at 是可選的
        )

        assert user.name == "測試使用者"
        assert user.email == "<EMAIL>"
        assert user.created_at is None


class TestPharmaceutical:
    """測試 Pharmaceutical 模型"""

    def test_pharmaceutical_creation(self):
        """測試藥物建立"""
        pharmaceutical = Pharmaceutical(
            name="測試藥物",
            formulation_type=FormulationType.PILL,
            dosage_per_unit=Decimal("10.50"),
            unit="mg",
            description="測試用藥物",
            created_at=datetime.now()
        )

        assert pharmaceutical.name == "測試藥物"
        assert pharmaceutical.formulation_type == FormulationType.PILL
        assert pharmaceutical.dosage_per_unit == Decimal("10.50")
        assert pharmaceutical.unit == "mg"
        assert pharmaceutical.description == "測試用藥物"
        assert pharmaceutical.created_at is not None

    def test_pharmaceutical_required_fields(self):
        """測試必要欄位"""
        # 測試所有必要欄位都存在
        pharmaceutical = Pharmaceutical(
            name="測試藥物",
            formulation_type=FormulationType.CAPSULE,
            dosage_per_unit=Decimal("25.0"),
            unit="mg"
        )

        assert pharmaceutical.name == "測試藥物"
        assert pharmaceutical.formulation_type == FormulationType.CAPSULE
        assert pharmaceutical.dosage_per_unit == Decimal("25.0")
        assert pharmaceutical.unit == "mg"

    def test_pharmaceutical_decimal_precision(self):
        """測試 Decimal 精度"""
        pharmaceutical = Pharmaceutical(
            name="精度測試藥物",
            formulation_type=FormulationType.PILL,
            dosage_per_unit=Decimal("123.456"),  # 3位小數
            unit="mg"
        )

        # 檢查 Decimal 類型
        assert isinstance(pharmaceutical.dosage_per_unit, Decimal)
        assert pharmaceutical.dosage_per_unit == Decimal("123.456")

    def test_pharmaceutical_formulation_types(self):
        """測試所有劑型類型"""
        for formulation_type in FormulationType:
            pharmaceutical = Pharmaceutical(
                name=f"測試藥物_{formulation_type.value}",
                formulation_type=formulation_type,
                dosage_per_unit=Decimal("10.0"),
                unit="mg"
            )
            assert pharmaceutical.formulation_type == formulation_type


class TestTake:
    """測試 Take 模型"""

    def test_take_creation(self):
        """測試服藥記錄建立"""
        taken_time = datetime.now()
        take = Take(
            user_id=1,
            pharmaceutical_id=1,
            quantity=Decimal("1.5"),
            taken_at=taken_time,
            notes="測試服藥記錄"
        )

        assert take.user_id == 1
        assert take.pharmaceutical_id == 1
        assert take.quantity == Decimal("1.5")
        assert take.taken_at == taken_time
        assert take.notes == "測試服藥記錄"

    def test_take_required_fields(self):
        """測試必要欄位"""
        taken_time = datetime.now()
        take = Take(
            user_id=1,
            pharmaceutical_id=1,
            quantity=Decimal("2.0"),
            taken_at=taken_time
            # notes 是可選的
        )

        assert take.user_id == 1
        assert take.pharmaceutical_id == 1
        assert take.quantity == Decimal("2.0")
        assert take.taken_at == taken_time
        assert take.notes is None

    def test_take_quantity_precision(self):
        """測試數量精度"""
        take = Take(
            user_id=1,
            pharmaceutical_id=1,
            quantity=Decimal("0.25"),  # 四分之一
            taken_at=datetime.now()
        )

        assert isinstance(take.quantity, Decimal)
        assert take.quantity == Decimal("0.25")

    def test_take_foreign_keys(self):
        """測試外鍵欄位"""
        take = Take(
            user_id=123,
            pharmaceutical_id=456,
            quantity=Decimal("1.0"),
            taken_at=datetime.now()
        )

        assert take.user_id == 123
        assert take.pharmaceutical_id == 456


class TestModelRelationships:
    """測試模型關聯"""

    def test_model_table_names(self):
        """測試資料表名稱"""
        # SQLModel 預設使用類別名稱的小寫作為資料表名稱
        assert User.__tablename__ == "user"
        assert Pharmaceutical.__tablename__ == "pharmaceutical"
        assert Take.__tablename__ == "take"

    def test_model_fields_exist(self):
        """測試模型欄位存在"""
        # 檢查 User 欄位
        user_fields = set(User.model_fields.keys())
        expected_user_fields = {"id", "name", "email", "created_at"}
        assert expected_user_fields.issubset(user_fields)

        # 檢查 Pharmaceutical 欄位
        pharmaceutical_fields = set(Pharmaceutical.model_fields.keys())
        expected_pharmaceutical_fields = {
            "id", "name", "formulation_type", "dosage_per_unit",
            "unit", "description", "created_at"
        }
        assert expected_pharmaceutical_fields.issubset(pharmaceutical_fields)

        # 檢查 Take 欄位
        take_fields = set(Take.model_fields.keys())
        expected_take_fields = {
            "id", "user_id", "pharmaceutical_id", "quantity",
            "taken_at", "notes"
        }
        assert expected_take_fields.issubset(take_fields)
