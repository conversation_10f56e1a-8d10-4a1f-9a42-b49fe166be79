"""
JWT 認證模組
"""
from datetime import datetime, timedelta, timezone
from typing import Optional

from fastapi import HTTP<PERSON>x<PERSON>, status
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
from sqlmodel import select

from .db import get_session
from .models import User

# JWT 設定
SECRET_KEY = "your-secret-key-change-this-in-production"  # 生產環境中應該使用環境變數
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 密碼加密設定
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    驗證密碼
    
    Args:
        plain_password: 明文密碼
        hashed_password: 加密後的密碼
        
    Returns:
        bool: 密碼是否正確
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    加密密碼
    
    Args:
        password: 明文密碼
        
    Returns:
        str: 加密後的密碼
    """
    return pwd_context.hash(password)


def authenticate_user(user_id: int, password: str) -> Optional[User]:
    """
    驗證使用者身份
    
    Args:
        user_id: 使用者 ID
        password: 密碼
        
    Returns:
        Optional[User]: 驗證成功返回使用者物件，否則返回 None
    """
    with get_session() as session:
        statement = select(User).where(User.id == user_id)
        user = session.exec(statement).first()
        
        if not user:
            return None
            
        if not verify_password(password, user.password_hash):
            return None
            
        return user


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None, original_issued_at: Optional[datetime] = None) -> str:
    """
    建立 JWT 存取權杖
    
    Args:
        data: 要編碼的資料
        expires_delta: 過期時間
        original_issued_at: 首次發給時間，若為None則為新登入
        
    Returns:
        str: JWT 權杖
    """
    to_encode = data.copy()
    now = datetime.now(timezone.utc)
    
    if expires_delta:
        expire = now + expires_delta
    else:
        expire = now + timedelta(minutes=15)
    
    # 記錄首次發給時間
    if original_issued_at is None:
        # 新登入，記錄當前時間
        issued_at = now
    else:
        # 權杖更新，保持原始發給時間
        issued_at = original_issued_at
        
        # 檢查是否超過12小時限制
        max_expire = issued_at + timedelta(hours=12)
        if expire > max_expire:
            expire = max_expire
        
    to_encode.update({
        "exp": expire.timestamp(),
        "iat": now.timestamp(),  # 當前發給時間
        "orig_iat": issued_at.timestamp()  # 首次發給時間
    })
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> dict:
    """
    驗證 JWT 權杖
    
    Args:
        token: JWT 權杖
        
    Returns:
        dict: 解碼後的資料，包含user_id, exp, orig_iat等
        
    Raises:
        HTTPException: 權杖無效時拋出異常
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="無法驗證憑證",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: int = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        return {
            "user_id": user_id,
            "exp": payload.get("exp"),
            "iat": payload.get("iat"),
            "orig_iat": payload.get("orig_iat")
        }
    except JWTError:
        raise credentials_exception


def refresh_access_token(token: str) -> str:
    """
    基於現有JWT更新權杖
    
    Args:
        token: 現有的JWT權杖
        
    Returns:
        str: 新的JWT權杖
        
    Raises:
        HTTPException: 權杖無效或超過12小時限制時拋出異常
    """
    try:
        # 驗證現有權杖（即使已過期也要能解析）
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM], options={"verify_exp": False})
        user_id = payload.get("sub")
        orig_iat = payload.get("orig_iat")
        
        if user_id is None or orig_iat is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="權杖資訊不完整",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 轉換時間戳為datetime
        original_issued_at = datetime.fromtimestamp(orig_iat, timezone.utc)
        
        # 檢查是否超過12小時
        now = datetime.now(timezone.utc)
        if now - original_issued_at > timedelta(hours=12):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="權杖已超過最大有效時間",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 驗證使用者是否存在
        with get_session() as session:
            statement = select(User).where(User.id == int(user_id))
            user = session.exec(statement).first()
            
            if user is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="使用者不存在",
                    headers={"WWW-Authenticate": "Bearer"},
                )
        
        # 生成新權杖
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        new_token = create_access_token(
            data={"sub": user_id}, 
            expires_delta=access_token_expires,
            original_issued_at=original_issued_at
        )
        
        return new_token
        
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="權杖格式無效",
            headers={"WWW-Authenticate": "Bearer"},
        )


def get_current_user(token: str) -> User:
    """
    從 JWT 權杖取得當前使用者
    
    Args:
        token: JWT 權杖
        
    Returns:
        User: 使用者物件
        
    Raises:
        HTTPException: 權杖無效或使用者不存在時拋出異常
    """
    token_data = verify_token(token)
    user_id = token_data["user_id"]
    
    with get_session() as session:
        statement = select(User).where(User.id == user_id)
        user = session.exec(statement).first()
        
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="使用者不存在",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return user
