import configparser
import os
from pathlib import Path
from typing import Dict, Optional

import yaml


def find_config_file() -> Dict[str, str]:
    """
    搜尋並讀取資料庫配置檔案

    搜尋順序：
    1. package平行目錄下
    2. 上一層目錄下（如果有且有權限存取的話）
    3. 當前執行環境cwd中

    yaml檔案優先於ini檔案

    Returns:
        Dict[str, str]: 包含 host, port, user, password 的字典

    Raises:
        FileNotFoundError: 找不到配置檔案
        ValueError: 配置檔案中缺少必要的資料庫設定
        PermissionError: 無權限讀取檔案
    """

    # 取得當前檔案所在的package目錄
    current_file_path = Path(__file__).resolve()
    package_dir = current_file_path.parent  # when_did_i_take 目錄

    # 定義搜尋路徑順序
    search_paths = [
        package_dir.parent,  # package平行目錄（專案根目錄）
        package_dir.parent.parent,  # 上一層目錄
        Path.cwd()  # 當前執行環境cwd
    ]

    # 配置檔案名稱，yaml優先於ini
    config_filenames = ["config.yaml", "config.yml", "config.ini"]

    for search_path in search_paths:
        # 檢查路徑是否存在且有權限存取
        if not search_path.exists() or not os.access(search_path, os.R_OK):
            continue

        for filename in config_filenames:
            config_file = search_path / filename

            if config_file.exists() and config_file.is_file():
                try:
                    # 找到檔案，嘗試讀取
                    if filename.endswith(('.yaml', '.yml')):
                        return _read_yaml_config(config_file)
                    elif filename.endswith('.ini'):
                        return _read_ini_config(config_file)
                except (PermissionError, ValueError) as e:
                    # 如果找到檔案但無法讀取或缺少必要設定，直接報錯
                    raise e

    # 所有路徑都搜尋完畢，沒有找到配置檔案
    raise FileNotFoundError(
        "找不到配置檔案 (config.yaml, config.yml, config.ini)。"
        f"已搜尋路徑: {[str(p) for p in search_paths]}"
    )


def _read_yaml_config(config_file: Path) -> Dict[str, str]:
    """
    讀取YAML配置檔案

    Args:
        config_file: 配置檔案路徑

    Returns:
        Dict[str, str]: 資料庫配置字典

    Raises:
        ValueError: 配置檔案格式錯誤或缺少必要設定
        PermissionError: 無權限讀取檔案
    """
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
    except PermissionError:
        raise PermissionError(f"無權限讀取配置檔案: {config_file}")
    except yaml.YAMLError as e:
        raise ValueError(f"YAML配置檔案格式錯誤 {config_file}: {e}")
    except Exception as e:
        raise ValueError(f"讀取配置檔案失敗 {config_file}: {e}")

    if not isinstance(config_data, dict):
        raise ValueError(f"配置檔案格式錯誤，應為字典格式: {config_file}")

    # 檢查是否有DB配置
    if 'DB' not in config_data:
        raise ValueError(f"配置檔案中找不到 'DB' 設定: {config_file}")

    db_config = config_data['DB']
    if not isinstance(db_config, dict):
        raise ValueError(f"DB配置應為字典格式: {config_file}")

    # 檢查必要的資料庫設定
    required_keys = ['host', 'port', 'user', 'password', 'database']
    missing_keys = [key for key in required_keys if key not in db_config]

    if missing_keys:
        raise ValueError(
            f"配置檔案 {config_file} 中的DB設定缺少必要欄位: {missing_keys}"
        )

    # 轉換為字串格式並返回
    return {key: str(db_config[key]) for key in required_keys}


def _read_ini_config(config_file: Path) -> Dict[str, str]:
    """
    讀取INI配置檔案

    Args:
        config_file: 配置檔案路徑

    Returns:
        Dict[str, str]: 資料庫配置字典

    Raises:
        ValueError: 配置檔案格式錯誤或缺少必要設定
        PermissionError: 無權限讀取檔案
    """
    config = configparser.ConfigParser()

    try:
        config.read(config_file, encoding='utf-8')
    except PermissionError:
        raise PermissionError(f"無權限讀取配置檔案: {config_file}")
    except configparser.Error as e:
        raise ValueError(f"INI配置檔案格式錯誤 {config_file}: {e}")
    except Exception as e:
        raise ValueError(f"讀取配置檔案失敗 {config_file}: {e}")

    # 檢查是否有DB section
    if 'DB' not in config:
        raise ValueError(f"配置檔案中找不到 '[DB]' section: {config_file}")

    db_section = config['DB']

    # 檢查必要的資料庫設定
    required_keys = ['host', 'port', 'user', 'password', 'database']
    missing_keys = [key for key in required_keys if key not in db_section]

    if missing_keys:
        raise ValueError(
            f"配置檔案 {config_file} 中的 [DB] section 缺少必要欄位: {missing_keys}"
        )

    # 返回資料庫配置
    return {key: db_section[key] for key in required_keys}
