from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import List, Optional

from sqlmodel import Field, Relationship, SQLModel


class FormulationType(str, Enum):
    CAPSULE = "Capsule"
    PILL = "Pill"
    INJECTION = "Injection"
    POWDER = "Powder"
    SYRUP = "Syrup"
    PATCH = "Patch"
    SPRAY = "Spray"
    TOPICAL = "Topical"


class User(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(min_length=1)
    email: str = Field(min_length=1)
    password_hash: str = Field(min_length=1)  # 儲存加密後的密碼
    created_at: Optional[datetime] = Field(default=None)

    takes: List["Take"] = Relationship(back_populates="user")
    take_plans: List["TakePlan"] = Relationship(back_populates="user")
    created_pharmaceuticals: List["Pharmaceutical"] = Relationship(back_populates="creator")


class Pharmaceutical(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(min_length=1)
    formulation_type: FormulationType
    dosage_per_serving: Decimal = Field(decimal_places=2)
    dosage_unit: str = Field(min_length=1)
    serving_unit_name: Optional[str] = Field(default=None, max_length=10)
    current_plan: bool = Field(default=True)
    description: Optional[str] = Field(default=None)
    created_by: int = Field(foreign_key="user.id")
    created_at: Optional[datetime] = Field(default=None)

    takes: List["Take"] = Relationship(back_populates="pharmaceutical")
    take_plans: List["TakePlan"] = Relationship(back_populates="pharmaceutical")
    creator: "User" = Relationship(back_populates="created_pharmaceuticals")


class Take(SQLModel, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")
    pharmaceutical_id: int = Field(foreign_key="pharmaceutical.id")
    quantity: Decimal = Field(decimal_places=2)
    taken_at: datetime
    notes: Optional[str] = Field(default=None)

    user: User = Relationship(back_populates="takes")
    pharmaceutical: Pharmaceutical = Relationship(back_populates="takes")


class TakePlan(SQLModel, table=True):
    """服藥計劃模型"""
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="user.id")
    pharmaceutical_id: int = Field(foreign_key="pharmaceutical.id")
    dosage: Decimal = Field(decimal_places=2, description="每次劑量")
    unit: str = Field(min_length=1, description="劑量單位")
    take_count: int = Field(description="每日服用次數")
    start_date: Optional[datetime] = Field(default=None, description="開始日期")
    end_date: Optional[datetime] = Field(default=None, description="結束日期")
    is_active: bool = Field(default=True, description="是否啟用")
    notes: Optional[str] = Field(default=None, description="備註")
    created_at: Optional[datetime] = Field(default=None)

    user: User = Relationship(back_populates="take_plans")
    pharmaceutical: Pharmaceutical = Relationship(back_populates="take_plans")
