"""
API 端點測試腳本
"""
import json

import requests


def test_root_endpoint():
    """測試根端點"""
    response = requests.get("http://localhost:8001/")
    print("根端點回應:")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    print()


def test_health_endpoint():
    """測試健康檢查端點"""
    response = requests.get("http://localhost:8001/health")
    print("健康檢查端點回應:")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    print()


def test_graphql_query():
    """測試 GraphQL 查詢"""
    query = """
    query {
        getTakes(userId: 1) {
            id
            quantity
            takenAt
            notes
            user {
                name
                email
            }
            pharmaceutical {
                name
                formulationType
                dosagePerUnit
                unit
            }
        }
    }
    """

    response = requests.post(
        "http://localhost:8001/graphql",
        json={"query": query},
        headers={"Content-Type": "application/json"}
    )

    print("GraphQL 查詢回應:")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    print()


def test_graphql_pharmaceuticals():
    """測試 GraphQL 藥物查詢"""
    query = """
    query {
        getPharmaceuticals {
            id
            name
            formulationType
            dosagePerUnit
            unit
            description
            takes {
                id
                quantity
                takenAt
                user {
                    name
                }
            }
        }
    }
    """

    response = requests.post(
        "http://localhost:8001/graphql",
        json={"query": query},
        headers={"Content-Type": "application/json"}
    )

    print("GraphQL 藥物查詢回應:")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    print()


def test_graphql_users():
    """測試 GraphQL 使用者查詢"""
    query = """
    query {
        getUsers {
            id
            name
            email
            takes {
                id
                quantity
                takenAt
                pharmaceutical {
                    name
                    formulationType
                }
            }
        }
    }
    """

    response = requests.post(
        "http://localhost:8001/graphql",
        json={"query": query},
        headers={"Content-Type": "application/json"}
    )

    print("GraphQL 使用者查詢回應:")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    print()


if __name__ == "__main__":
    print("=== API 端點測試 ===\n")

    try:
        test_root_endpoint()
        test_health_endpoint()
        test_graphql_users()
        test_graphql_pharmaceuticals()
        test_graphql_query()
        print("✅ 所有測試完成")
    except requests.exceptions.ConnectionError:
        print("❌ 無法連接到伺服器，請確保伺服器正在運行")
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
