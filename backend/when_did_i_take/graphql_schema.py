from datetime import datetime
from decimal import Decimal
from typing import List, Optional

import strawberry
from sqlmodel import and_, select
from strawberry.types import Info

from .db import get_session
from .graphql_auth import IsAuthenticated, IsOwner, require_authentication
from .models import FormulationType, Pharmaceutical, Take, User


@strawberry.type
class UserType:
    id: int
    name: str
    email: str

    @strawberry.field
    def takes(self) -> List["TakeType"]:
        """取得此使用者的所有服藥記錄"""
        with get_session() as session:
            statement = select(Take).where(Take.user_id == self.id)
            takes = session.exec(statement).all()
            return [
                TakeType(
                    id=take.id,
                    user_id=take.user_id,
                    pharmaceutical_id=take.pharmaceutical_id,
                    quantity=take.quantity,
                    taken_at=take.taken_at,
                    notes=take.notes
                ) for take in takes
            ]


@strawberry.type
class PharmaceuticalType:
    id: int
    name: str
    formulation_type: FormulationType
    dosage_per_serving: float
    dosage_unit: str
    serving_unit_name: Optional[str]
    current_plan: bool
    description: Optional[str]
    created_by: int

    @strawberry.field
    def takes(self) -> List["TakeType"]:
        """取得此藥物的所有服藥記錄"""
        with get_session() as session:
            statement = select(Take).where(Take.pharmaceutical_id == self.id)
            takes = session.exec(statement).all()
            return [
                TakeType(
                    id=take.id,
                    user_id=take.user_id,
                    pharmaceutical_id=take.pharmaceutical_id,
                    quantity=take.quantity,
                    taken_at=take.taken_at,
                    notes=take.notes
                ) for take in takes
            ]


@strawberry.type
class TakeType:
    id: int
    user_id: int
    pharmaceutical_id: int
    quantity: float
    taken_at: datetime
    notes: Optional[str]

    @strawberry.field
    def user(self) -> Optional[UserType]:
        """取得服藥的使用者資訊"""
        with get_session() as session:
            statement = select(User).where(User.id == self.user_id)
            user = session.exec(statement).first()
            if user:
                return UserType(
                    id=user.id,
                    name=user.name,
                    email=user.email
                )
            return None

    @strawberry.field
    def pharmaceutical(self) -> Optional[PharmaceuticalType]:
        """取得服藥的藥物資訊"""
        with get_session() as session:
            statement = select(Pharmaceutical).where(Pharmaceutical.id == self.pharmaceutical_id)
            pharmaceutical = session.exec(statement).first()
            if pharmaceutical:
                return PharmaceuticalType(
                    id=pharmaceutical.id,
                    name=pharmaceutical.name,
                    formulation_type=pharmaceutical.formulation_type,
                    dosage_per_serving=pharmaceutical.dosage_per_serving,
                    dosage_unit=pharmaceutical.dosage_unit,
                    serving_unit_name=pharmaceutical.serving_unit_name,
                    current_plan=pharmaceutical.current_plan,
                    description=pharmaceutical.description,
                    created_by=pharmaceutical.created_by
                )
            return None


@strawberry.input
class PharmaceuticalInput:
    name: str
    formulation_type: FormulationType
    dosage_per_serving: float
    dosage_unit: str
    serving_unit_name: Optional[str] = None
    current_plan: Optional[bool] = True
    description: Optional[str] = None


@strawberry.input
class PharmaceuticalUpdateInput:
    id: int
    name: Optional[str] = None
    formulation_type: Optional[FormulationType] = None
    dosage_per_serving: Optional[float] = None
    dosage_unit: Optional[str] = None
    serving_unit_name: Optional[str] = None
    current_plan: Optional[bool] = None
    description: Optional[str] = None


@strawberry.input
class TakeInput:
    user_id: int
    pharmaceutical_id: int
    quantity: float
    taken_at: datetime
    notes: Optional[str] = None


@strawberry.input
class TakeUpdateInput:
    id: int
    quantity: Optional[float] = None
    taken_at: Optional[datetime] = None
    notes: Optional[str] = None


@strawberry.type
class Query:
    @strawberry.field
    def get_takes(
        self,
        info: Info,
        pharmaceutical_id: Optional[int] = None,
        from_time: Optional[datetime] = None,
        to_time: Optional[datetime] = None,
        offset: int = 0,
        limit: int = 10
    ) -> List[TakeType]:
        # 檢查認證並自動使用當前使用者的資料
        current_user = require_authentication(info)

        with get_session() as session:
            query = select(Take).where(Take.user_id == current_user.id)

            if pharmaceutical_id:
                query = query.where(Take.pharmaceutical_id == pharmaceutical_id)

            if from_time and to_time:
                query = query.where(and_(Take.taken_at >= from_time, Take.taken_at <= to_time))
            elif from_time:
                query = query.where(Take.taken_at >= from_time)
            elif to_time:
                query = query.where(Take.taken_at <= to_time)

            query = query.offset(offset).limit(limit).order_by(Take.taken_at.desc())
            takes = session.exec(query).all()

            return [
                TakeType(
                    id=take.id,
                    user_id=take.user_id,
                    pharmaceutical_id=take.pharmaceutical_id,
                    quantity=float(take.quantity),
                    taken_at=take.taken_at,
                    notes=take.notes
                )
                for take in takes
            ]

    @strawberry.field
    def get_pharmaceuticals(self, info: Info, current_plan_only: Optional[bool] = None) -> List[PharmaceuticalType]:
        # 需要認證才能查看藥物列表，只能查看自己建立的藥物
        current_user = require_authentication(info)
        with get_session() as session:
            query = select(Pharmaceutical).where(Pharmaceutical.created_by == current_user.id)
            
            # 如果指定了 current_plan_only，則加入過濾條件
            if current_plan_only is not None:
                query = query.where(Pharmaceutical.current_plan == current_plan_only)
                
            pharmaceuticals = session.exec(query).all()
            return [
                PharmaceuticalType(
                    id=pharm.id,
                    name=pharm.name,
                    formulation_type=pharm.formulation_type,
                    dosage_per_serving=float(pharm.dosage_per_serving),
                    dosage_unit=pharm.dosage_unit,
                    serving_unit_name=pharm.serving_unit_name,
                    current_plan=pharm.current_plan,
                    description=pharm.description,
                    created_by=pharm.created_by
                )
                for pharm in pharmaceuticals
            ]

    @strawberry.field
    def get_user(self, info: Info) -> UserType:
        # 取得當前使用者資訊
        current_user = require_authentication(info)
        return UserType(
            id=current_user.id,
            name=current_user.name,
            email=current_user.email
        )


@strawberry.type
class Mutation:
    @strawberry.mutation
    def create_pharmaceutical(self, info: Info, pharmaceutical: PharmaceuticalInput) -> PharmaceuticalType:
        # 需要認證才能建立藥物
        current_user = require_authentication(info)
        with get_session() as session:
            # 檢查名稱是否重複（僅在該使用者的藥物中檢查）
            existing = session.exec(
                select(Pharmaceutical).where(
                    and_(
                        Pharmaceutical.name == pharmaceutical.name,
                        Pharmaceutical.created_by == current_user.id
                    )
                )
            ).first()
            if existing:
                raise ValueError(f"Pharmaceutical with name '{pharmaceutical.name}' already exists")

            new_pharmaceutical = Pharmaceutical(
                name=pharmaceutical.name,
                formulation_type=pharmaceutical.formulation_type,
                dosage_per_serving=Decimal(str(pharmaceutical.dosage_per_serving)),
                dosage_unit=pharmaceutical.dosage_unit,
                serving_unit_name=pharmaceutical.serving_unit_name,
                current_plan=pharmaceutical.current_plan if pharmaceutical.current_plan is not None else True,
                description=pharmaceutical.description,
                created_by=current_user.id
            )
            session.add(new_pharmaceutical)
            session.commit()
            session.refresh(new_pharmaceutical)

            return PharmaceuticalType(
                id=new_pharmaceutical.id,
                name=new_pharmaceutical.name,
                formulation_type=new_pharmaceutical.formulation_type,
                dosage_per_serving=float(new_pharmaceutical.dosage_per_serving),
                dosage_unit=new_pharmaceutical.dosage_unit,
                serving_unit_name=new_pharmaceutical.serving_unit_name,
                current_plan=new_pharmaceutical.current_plan,
                description=new_pharmaceutical.description,
                created_by=new_pharmaceutical.created_by
            )

    @strawberry.mutation
    def update_pharmaceutical(self, info: Info, pharmaceutical: PharmaceuticalUpdateInput) -> PharmaceuticalType:
        # 需要認證才能更新藥物
        current_user = require_authentication(info)
        with get_session() as session:
            existing = session.get(Pharmaceutical, pharmaceutical.id)
            if not existing:
                raise ValueError(f"Pharmaceutical with id {pharmaceutical.id} not found")
            
            # 檢查是否為該使用者建立的藥物
            if existing.created_by != current_user.id:
                raise ValueError("You can only update pharmaceuticals you created")

            # 檢查名稱是否與其他記錄重複（僅在該使用者的藥物中檢查）
            if pharmaceutical.name and pharmaceutical.name != existing.name:
                name_exists = session.exec(
                    select(Pharmaceutical).where(
                        and_(
                            Pharmaceutical.name == pharmaceutical.name,
                            Pharmaceutical.id != pharmaceutical.id,
                            Pharmaceutical.created_by == current_user.id
                        )
                    )
                ).first()
                if name_exists:
                    raise ValueError(f"Pharmaceutical with name '{pharmaceutical.name}' already exists")

            # 更新欄位
            if pharmaceutical.name is not None:
                existing.name = pharmaceutical.name
            if pharmaceutical.formulation_type is not None:
                existing.formulation_type = pharmaceutical.formulation_type
            if pharmaceutical.dosage_per_serving is not None:
                existing.dosage_per_serving = Decimal(str(pharmaceutical.dosage_per_serving))
            if pharmaceutical.dosage_unit is not None:
                existing.dosage_unit = pharmaceutical.dosage_unit
            if pharmaceutical.serving_unit_name is not None:
                existing.serving_unit_name = pharmaceutical.serving_unit_name
            if pharmaceutical.current_plan is not None:
                existing.current_plan = pharmaceutical.current_plan
            if pharmaceutical.description is not None:
                existing.description = pharmaceutical.description

            session.add(existing)
            session.commit()
            session.refresh(existing)

            return PharmaceuticalType(
                id=existing.id,
                name=existing.name,
                formulation_type=existing.formulation_type,
                dosage_per_serving=float(existing.dosage_per_serving),
                dosage_unit=existing.dosage_unit,
                serving_unit_name=existing.serving_unit_name,
                current_plan=existing.current_plan,
                description=existing.description,
                created_by=existing.created_by
            )

    @strawberry.mutation
    def delete_pharmaceutical(self, info: Info, id: int) -> bool:
        # 需要認證才能刪除藥物
        current_user = require_authentication(info)
        with get_session() as session:
            pharmaceutical = session.get(Pharmaceutical, id)
            if not pharmaceutical:
                raise ValueError(f"Pharmaceutical with id {id} not found")
            
            # 檢查是否為該使用者建立的藥物
            if pharmaceutical.created_by != current_user.id:
                raise ValueError("You can only delete pharmaceuticals you created")

            # 檢查是否有服用記錄
            existing_takes = session.exec(
                select(Take).where(Take.pharmaceutical_id == id)
            ).first()
            
            if existing_takes:
                raise ValueError(f"Cannot delete pharmaceutical '{pharmaceutical.name}' because it has existing take records")

            session.delete(pharmaceutical)
            session.commit()
            return True

    @strawberry.mutation
    def create_take(self, info: Info, take: TakeInput) -> TakeType:
        # 檢查認證並確保使用者只能為自己建立服藥記錄
        current_user = require_authentication(info)
        if current_user.id != take.user_id:
            raise Exception("只能為自己建立服藥記錄")
        with get_session() as session:
            # 驗證 user 和 pharmaceutical 存在
            user = session.get(User, take.user_id)
            if not user:
                raise ValueError(f"User with id {take.user_id} not found")

            pharmaceutical = session.get(Pharmaceutical, take.pharmaceutical_id)
            if not pharmaceutical:
                raise ValueError(f"Pharmaceutical with id {take.pharmaceutical_id} not found")
            
            # 檢查藥物是否為該使用者建立的
            if pharmaceutical.created_by != current_user.id:
                raise ValueError("You can only create take records for pharmaceuticals you created")
            
            # 檢查同一分鐘內是否已有相同藥物的服藥紀錄
            from datetime import timedelta
            taken_minute_start = take.taken_at.replace(second=0, microsecond=0)
            taken_minute_end = taken_minute_start + timedelta(minutes=1)
            
            existing_take = session.exec(
                select(Take).where(
                    and_(
                        Take.user_id == take.user_id,
                        Take.pharmaceutical_id == take.pharmaceutical_id,
                        Take.taken_at >= taken_minute_start,
                        Take.taken_at < taken_minute_end
                    )
                )
            ).first()
            
            if existing_take:
                raise ValueError(f"您已在 {taken_minute_start.strftime('%H:%M')} 服用過此藥物，不能在同一分鐘內重複服用")

            new_take = Take(
                user_id=take.user_id,
                pharmaceutical_id=take.pharmaceutical_id,
                quantity=Decimal(str(take.quantity)),
                taken_at=take.taken_at,
                notes=take.notes
            )
            session.add(new_take)
            session.commit()
            session.refresh(new_take)

            return TakeType(
                id=new_take.id,
                user_id=new_take.user_id,
                pharmaceutical_id=new_take.pharmaceutical_id,
                quantity=float(new_take.quantity),
                taken_at=new_take.taken_at,
                notes=new_take.notes
            )

    @strawberry.mutation
    def update_take(self, info: Info, take: TakeUpdateInput) -> TakeType:
        # 檢查認證並確保使用者只能更新自己的服藥記錄
        current_user = require_authentication(info)
        with get_session() as session:
            existing_take = session.get(Take, take.id)
            if not existing_take:
                raise ValueError(f"Take with id {take.id} not found")
            
            # 檢查是否為該使用者的服藥記錄
            if existing_take.user_id != current_user.id:
                raise ValueError("You can only update your own take records")
            
            # 更新欄位
            if take.quantity is not None:
                existing_take.quantity = Decimal(str(take.quantity))
            if take.taken_at is not None:
                existing_take.taken_at = take.taken_at
            if take.notes is not None:
                existing_take.notes = take.notes
            
            session.commit()
            session.refresh(existing_take)
            
            return TakeType(
                id=existing_take.id,
                user_id=existing_take.user_id,
                pharmaceutical_id=existing_take.pharmaceutical_id,
                quantity=float(existing_take.quantity),
                taken_at=existing_take.taken_at,
                notes=existing_take.notes
            )

    @strawberry.mutation
    def delete_take(self, info: Info, take_id: int) -> bool:
        # 檢查認證並確保使用者只能刪除自己的服藥記錄
        current_user = require_authentication(info)
        with get_session() as session:
            take = session.get(Take, take_id)
            if not take:
                return False
            
            # 檢查是否為該使用者的服藥記錄
            if take.user_id != current_user.id:
                raise ValueError("You can only delete your own take records")

            session.delete(take)
            session.commit()
            return True


schema = strawberry.Schema(query=Query, mutation=Mutation)
